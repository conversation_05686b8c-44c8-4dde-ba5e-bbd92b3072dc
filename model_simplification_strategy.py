# model_simplification_strategy.py - 模型简化策略
"""
基于参数分布分析的系统性模型简化方案
目标：将45万参数降至10-15万参数，同时保持预测精度
"""

# ==================== 简化后的超参数配置 ====================

# 原始配置 vs 简化配置对比
ORIGINAL_CONFIG = {
    "SPATIAL_EMBEDDING_DIM": 64,    # 空间嵌入维度
    "FUTURE_EMBEDDING_DIM": 64,     # 未来嵌入维度  
    "HIDDEN_DIM": 128,              # 隐藏层维度
    "LSTM_HIDDEN_DIM": 64,          # LSTM隐藏维度
    "LSTM_NUM_LAYERS": 2,           # LSTM层数
    "NUM_GNN_LAYERS": 3,            # GNN层数
    "TIME_SLICES": 5,               # 时间切片数
    "total_params": 450947
}

# 激进简化方案（目标：10万参数）
AGGRESSIVE_SIMPLIFICATION = {
    "SPATIAL_EMBEDDING_DIM": 32,    # 64 → 32 (减半)
    "FUTURE_EMBEDDING_DIM": 32,     # 64 → 32 (减半)
    "HIDDEN_DIM": 64,               # 128 → 64 (减半)
    "LSTM_HIDDEN_DIM": 32,          # 64 → 32 (减半)
    "LSTM_NUM_LAYERS": 1,           # 2 → 1 (减半)
    "NUM_GNN_LAYERS": 2,            # 3 → 2 (减少1层)
    "TIME_SLICES": 3,               # 5 → 3 (减少2个切片)
    "expected_params": "~100,000"
}

# 保守简化方案（目标：15万参数）
CONSERVATIVE_SIMPLIFICATION = {
    "SPATIAL_EMBEDDING_DIM": 48,    # 64 → 48 (25%减少)
    "FUTURE_EMBEDDING_DIM": 48,     # 64 → 48 (25%减少)
    "HIDDEN_DIM": 96,               # 128 → 96 (25%减少)
    "LSTM_HIDDEN_DIM": 48,          # 64 → 48 (25%减少)
    "LSTM_NUM_LAYERS": 1,           # 2 → 1 (减半)
    "NUM_GNN_LAYERS": 2,            # 3 → 2 (减少1层)
    "TIME_SLICES": 4,               # 5 → 4 (减少1个切片)
    "expected_params": "~150,000"
}

# ==================== 架构级简化策略 ====================

ARCHITECTURAL_SIMPLIFICATIONS = {
    "replace_bilstm_with_attention": {
        "description": "用轻量级注意力机制替代BiLSTM",
        "rationale": "BiLSTM占48%参数，但路径只有4个链路，序列很短",
        "implementation": "MultiHeadAttention + 简单MLP",
        "expected_param_reduction": "70%"
    },
    
    "simplify_future_encoder": {
        "description": "简化GRU未来编码器",
        "rationale": "只有5个时间切片，不需要复杂的GRU",
        "implementation": "1D卷积 + 全连接层",
        "expected_param_reduction": "60%"
    },
    
    "feature_dimension_reduction": {
        "description": "降低特征维度",
        "rationale": "64维嵌入对于简单回归任务过高",
        "implementation": "32维嵌入 + 更少的隐藏层",
        "expected_param_reduction": "50%"
    },
    
    "remove_redundant_features": {
        "description": "移除冗余特征计算",
        "rationale": "某些特征工程可能不必要",
        "implementation": "特征重要性分析 + 选择性保留",
        "expected_param_reduction": "20%"
    }
}

# ==================== 具体实施方案 ====================

def get_simplified_model_config(simplification_level="conservative"):
    """
    获取简化后的模型配置
    
    Args:
        simplification_level: "aggressive" | "conservative" | "custom"
    
    Returns:
        简化后的配置字典
    """
    
    if simplification_level == "aggressive":
        return AGGRESSIVE_SIMPLIFICATION
    elif simplification_level == "conservative":
        return CONSERVATIVE_SIMPLIFICATION
    else:
        # 自定义配置
        return {
            "SPATIAL_EMBEDDING_DIM": 40,
            "FUTURE_EMBEDDING_DIM": 40,
            "HIDDEN_DIM": 80,
            "LSTM_HIDDEN_DIM": 40,
            "LSTM_NUM_LAYERS": 1,
            "NUM_GNN_LAYERS": 2,
            "TIME_SLICES": 4,
            "expected_params": "~120,000"
        }

# ==================== 替代架构设计 ====================

class LightweightPathAggregator:
    """轻量级路径聚合器，替代BiLSTM"""
    
    def __init__(self, input_dim, output_dim):
        """
        使用注意力机制替代BiLSTM进行路径聚合
        
        参数量对比：
        - BiLSTM: ~200K参数
        - 注意力机制: ~20K参数 (10倍减少)
        """
        self.attention = nn.MultiHeadAttention(
            embed_dim=input_dim,
            num_heads=4,
            batch_first=True
        )
        self.norm = nn.LayerNorm(input_dim)
        self.mlp = nn.Sequential(
            nn.Linear(input_dim, output_dim),
            nn.ReLU(),
            nn.Linear(output_dim, output_dim)
        )
    
    def forward(self, path_embeddings):
        """
        Args:
            path_embeddings: [batch_size, path_length, embedding_dim]
        Returns:
            aggregated_embedding: [batch_size, output_dim]
        """
        # 自注意力聚合
        attended, _ = self.attention(path_embeddings, path_embeddings, path_embeddings)
        attended = self.norm(attended + path_embeddings)
        
        # 全局平均池化
        pooled = attended.mean(dim=1)  # [batch_size, embedding_dim]
        
        # MLP投影
        output = self.mlp(pooled)
        return output

class SimplifiedFutureEncoder:
    """简化的未来编码器，替代GRU"""
    
    def __init__(self, time_slices, output_dim):
        """
        使用1D卷积替代GRU进行时间序列编码
        
        参数量对比：
        - GRU: ~170K参数
        - 1D卷积: ~10K参数 (17倍减少)
        """
        self.conv1d = nn.Conv1d(
            in_channels=1,
            out_channels=16,
            kernel_size=3,
            padding=1
        )
        self.pool = nn.AdaptiveAvgPool1d(1)
        self.mlp = nn.Sequential(
            nn.Linear(16, output_dim),
            nn.ReLU(),
            nn.Linear(output_dim, output_dim)
        )
    
    def forward(self, time_features):
        """
        Args:
            time_features: [batch_size, time_slices]
        Returns:
            encoded_features: [batch_size, output_dim]
        """
        # 添加通道维度
        x = time_features.unsqueeze(1)  # [batch_size, 1, time_slices]
        
        # 1D卷积
        x = F.relu(self.conv1d(x))  # [batch_size, 16, time_slices]
        
        # 全局平均池化
        x = self.pool(x).squeeze(-1)  # [batch_size, 16]
        
        # MLP编码
        output = self.mlp(x)
        return output

# ==================== 简化效果预估 ====================

def estimate_simplification_impact():
    """估算简化方案的效果"""
    
    original_params = 450947
    
    simplification_scenarios = {
        "保守简化": {
            "target_params": 150000,
            "param_reduction": "67%",
            "expected_speedup": "2-3x",
            "expected_accuracy_loss": "5-10%",
            "risk_level": "低"
        },
        
        "激进简化": {
            "target_params": 100000,
            "param_reduction": "78%",
            "expected_speedup": "3-4x",
            "expected_accuracy_loss": "10-20%",
            "risk_level": "中"
        },
        
        "架构重设计": {
            "target_params": 80000,
            "param_reduction": "82%",
            "expected_speedup": "4-5x",
            "expected_accuracy_loss": "15-25%",
            "risk_level": "高"
        }
    }
    
    return simplification_scenarios

# ==================== 实施建议 ====================

IMPLEMENTATION_ROADMAP = {
    "阶段1_立即可行": {
        "actions": [
            "降低嵌入维度：64→48",
            "减少LSTM层数：2→1", 
            "减少时间切片：5→4"
        ],
        "expected_reduction": "40%",
        "implementation_time": "1天",
        "risk": "极低"
    },
    
    "阶段2_中期优化": {
        "actions": [
            "用注意力替代BiLSTM",
            "简化GRU编码器",
            "进一步降维到32维"
        ],
        "expected_reduction": "70%",
        "implementation_time": "3-5天",
        "risk": "中等"
    },
    
    "阶段3_深度重构": {
        "actions": [
            "重新设计整体架构",
            "移除冗余特征工程",
            "端到端优化"
        ],
        "expected_reduction": "80%",
        "implementation_time": "1-2周",
        "risk": "较高"
    }
}

def recommend_simplification_strategy(current_performance, target_speedup):
    """
    基于当前性能和目标提速推荐简化策略
    
    Args:
        current_performance: 当前模型性能指标
        target_speedup: 目标提速倍数
    
    Returns:
        推荐的简化策略
    """
    
    if target_speedup <= 2:
        return "保守简化：降维 + 减层，风险最低"
    elif target_speedup <= 3:
        return "中等简化：架构调整 + 降维，平衡风险和收益"
    else:
        return "激进简化：重新设计架构，需要充分测试"

if __name__ == "__main__":
    print("=== 模型简化策略分析 ===")
    
    # 显示简化方案对比
    scenarios = estimate_simplification_impact()
    for name, details in scenarios.items():
        print(f"\n{name}:")
        for key, value in details.items():
            print(f"  {key}: {value}")
    
    # 推荐实施路径
    print(f"\n=== 推荐实施路径 ===")
    for phase, details in IMPLEMENTATION_ROADMAP.items():
        print(f"\n{phase}:")
        print(f"  操作: {', '.join(details['actions'])}")
        print(f"  预期减少: {details['expected_reduction']}")
        print(f"  实施时间: {details['implementation_time']}")
        print(f"  风险等级: {details['risk']}")
