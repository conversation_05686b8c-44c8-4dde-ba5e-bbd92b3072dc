# enhanced_data_generation_config.py - 增强数据生成配置
"""
重新生成更丰富数据的配置方案
相比简单的数据增强，这种方法能生成真实的、多样化的网络场景
"""

# 基础配置 - 大幅增加数据量和多样性
ENHANCED_PARAMS = {
    # 批次数量配置 - 显著增加
    "num_train": 1000,      # 200 → 1000: 5倍训练数据
    "num_validation": 300,  # 100 → 300: 3倍验证数据  
    "num_test": 50,         # 1 → 50: 更全面的测试
    
    # 多场景配置策略
    "scenario_configs": {
        # 轻载场景 (30%的训练数据)
        "light_load": {
            "rar_ops_per_batch": 8,                      # 较少操作
            "rar_group_size_range": (1, 6),             # 较小组
            "time_between_rars_range": (0.005, 0.08),   # 较长间隔
            "burst_probability": 0.3,                    # 低突发
            "rar_congestion_control": {
                "uplink_congestion_ratio": 1.0,         # 无拥塞
                "downlink_congestion_ratio": 1.0,
                "bottleneck_sharing_mode": "distributed"
            }
        },
        
        # 中载场景 (40%的训练数据)
        "medium_load": {
            "rar_ops_per_batch": 15,                    # 中等操作数
            "rar_group_size_range": (3, 10),           # 中等组大小
            "time_between_rars_range": (0.002, 0.04),  # 中等间隔
            "burst_probability": 0.6,                   # 中等突发
            "rar_congestion_control": {
                "uplink_congestion_ratio": 0.85,        # 轻微拥塞
                "downlink_congestion_ratio": 0.85,
                "bottleneck_sharing_mode": "mixed"      # 混合模式
            }
        },
        
        # 重载场景 (30%的训练数据)
        "heavy_load": {
            "rar_ops_per_batch": 25,                    # 高操作数
            "rar_group_size_range": (5, 16),           # 大组
            "time_between_rars_range": (0.0005, 0.02), # 短间隔
            "burst_probability": 0.9,                   # 高突发
            "rar_congestion_control": {
                "uplink_congestion_ratio": 0.6,         # 严重拥塞
                "downlink_congestion_ratio": 0.6,
                "bottleneck_sharing_mode": "concentrated"
            }
        }
    },
    
    # 流量模式多样化
    "flow_patterns": {
        # 小流模式 (20%数据)
        "small_flows": {
            "flow_size_range": (64, 1024),              # 64B - 1KB
            "flow_size_distribution": "exponential",
            "inter_arrival_pattern": "poisson"
        },
        
        # 中等流模式 (50%数据)  
        "medium_flows": {
            "flow_size_range": (1024, 65536),          # 1KB - 64KB
            "flow_size_distribution": "lognormal",
            "inter_arrival_pattern": "bursty"
        },
        
        # 大流模式 (30%数据)
        "large_flows": {
            "flow_size_range": (65536, 1048576),       # 64KB - 1MB
            "flow_size_distribution": "pareto",
            "inter_arrival_pattern": "periodic"
        }
    },
    
    # 网络状态变化模拟
    "network_dynamics": {
        "enable_link_failures": True,                   # 启用链路故障
        "failure_probability": 0.05,                    # 5%故障概率
        "enable_bandwidth_variation": True,             # 启用带宽变化
        "bandwidth_variation_range": (0.7, 1.3),       # 70%-130%带宽
        "enable_latency_variation": True,               # 启用延迟变化
        "base_latency_us": 10,                          # 基础延迟10μs
        "latency_variation_range": (0.8, 2.0)          # 80%-200%延迟
    },
    
    # 工作负载多样化
    "workload_diversity": {
        "ml_training": {
            "weight": 0.4,                              # 40%为ML训练流量
            "flow_characteristics": {
                "size_pattern": "gradient_sync",        # 梯度同步模式
                "timing_pattern": "synchronized",       # 同步模式
                "priority": "high"
            }
        },
        
        "data_transfer": {
            "weight": 0.3,                              # 30%为数据传输
            "flow_characteristics": {
                "size_pattern": "bulk_transfer",        # 批量传输
                "timing_pattern": "asynchronous",       # 异步模式
                "priority": "medium"
            }
        },
        
        "interactive": {
            "weight": 0.3,                              # 30%为交互式流量
            "flow_characteristics": {
                "size_pattern": "request_response",     # 请求响应模式
                "timing_pattern": "reactive",           # 反应式
                "priority": "low"
            }
        }
    },
    
    # 全局配置
    "output_base_dir": "data_gen/enhanced_datasets",
    "temp_dir": "enhanced_temp",
    
    # 拓扑配置保持不变
    "topology": {
        "pods": 4,
        "switches_per_pod": 4,
        "hosts_per_switch": 4,
        "ps_bandwidth": 16000,
        "sh_bandwidth": 16000
    },
    
    # 仿真配置 - 提高精度
    "simulation": {
        "timestep": 0.00005,                            # 50μs时间步长，提高精度
        "max_time": 500.0,
        "enable_detailed_logging": True,                # 启用详细日志
        "congestion_window_tracking": True              # 跟踪拥塞窗口
    }
}

# 数据生成策略
DATA_GENERATION_STRATEGY = {
    "generation_method": "stratified_sampling",         # 分层采样
    "scenario_distribution": {
        "light_load": 0.3,                             # 30%轻载
        "medium_load": 0.4,                            # 40%中载  
        "heavy_load": 0.3                              # 30%重载
    },
    
    "flow_pattern_distribution": {
        "small_flows": 0.2,                            # 20%小流
        "medium_flows": 0.5,                           # 50%中等流
        "large_flows": 0.3                             # 30%大流
    },
    
    "temporal_diversity": {
        "enable_time_series_generation": True,         # 启用时间序列生成
        "time_window_variations": [                     # 不同时间窗口
            (0.0, 100.0),      # 早期阶段
            (100.0, 300.0),    # 中期阶段  
            (300.0, 500.0)     # 后期阶段
        ],
        "seasonal_patterns": True                       # 季节性模式
    }
}

# 质量保证配置
QUALITY_ASSURANCE = {
    "data_validation": {
        "check_path_validity": True,                    # 检查路径有效性
        "check_timing_constraints": True,              # 检查时间约束
        "check_flow_conservation": True,               # 检查流守恒
        "min_samples_per_scenario": 100                # 每场景最少样本数
    },
    
    "diversity_metrics": {
        "measure_temporal_diversity": True,            # 测量时间多样性
        "measure_spatial_diversity": True,             # 测量空间多样性
        "measure_load_diversity": True,                # 测量负载多样性
        "target_unique_patterns": 5000                 # 目标唯一模式数
    },
    
    "balance_checking": {
        "check_class_balance": True,                   # 检查类别平衡
        "check_feature_distribution": True,           # 检查特征分布
        "rebalance_if_needed": True                    # 需要时重平衡
    }
}

def get_config_for_scenario(scenario_name):
    """获取特定场景的配置"""
    base_config = ENHANCED_PARAMS.copy()
    scenario_config = ENHANCED_PARAMS["scenario_configs"][scenario_name]
    
    # 合并配置
    base_config.update(scenario_config)
    return base_config

def estimate_generation_time():
    """估算数据生成时间"""
    total_batches = (ENHANCED_PARAMS["num_train"] + 
                    ENHANCED_PARAMS["num_validation"] + 
                    ENHANCED_PARAMS["num_test"])
    
    # 假设每批次生成时间为30秒（基于当前复杂度）
    estimated_seconds = total_batches * 30
    estimated_hours = estimated_seconds / 3600
    
    return {
        "total_batches": total_batches,
        "estimated_time_hours": estimated_hours,
        "estimated_samples": total_batches * 60,  # 假设每批次60个流
        "diversity_improvement": "10-20x"
    }

def compare_with_augmentation():
    """对比数据增强方案"""
    return {
        "data_augmentation": {
            "method": "添加噪声到现有数据",
            "data_volume": "6倍增加",
            "diversity": "有限（基于现有模式）",
            "realism": "中等（人工扰动）",
            "time_cost": "低（几小时）"
        },
        
        "enhanced_generation": {
            "method": "重新仿真生成",
            "data_volume": "5倍增加",
            "diversity": "高（全新场景）",
            "realism": "高（物理仿真）",
            "time_cost": "高（约40小时）"
        },
        
        "recommendation": "重新生成数据，因为质量提升远超时间成本"
    }

if __name__ == "__main__":
    print("=== 增强数据生成配置分析 ===")
    
    estimation = estimate_generation_time()
    print(f"预估生成时间: {estimation['estimated_time_hours']:.1f}小时")
    print(f"预估样本数量: {estimation['estimated_samples']:,}")
    print(f"多样性提升: {estimation['diversity_improvement']}")
    
    print("\n=== 方案对比 ===")
    comparison = compare_with_augmentation()
    for method, details in comparison.items():
        if method != "recommendation":
            print(f"\n{method}:")
            for key, value in details.items():
                print(f"  {key}: {value}")
    
    print(f"\n推荐: {comparison['recommendation']}")
