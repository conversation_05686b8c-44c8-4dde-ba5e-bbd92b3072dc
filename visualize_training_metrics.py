#!/usr/bin/env python3
"""
visualize_training_metrics.py

Example script to demonstrate how to use the training metrics visualization functionality.
This script shows how to call the visualization functions for training metrics analysis.
"""

import json
import os
from visualization import plot_training_metrics_four_subplots

def main():
    """Main function to demonstrate training metrics visualization."""
    
    # Input file path
    metrics_file = 'training_metric.json'
    output_dir = 'visualizations'
    
    # Check if metrics file exists
    if not os.path.exists(metrics_file):
        print(f"Error: Could not find training metrics file {metrics_file}")
        print("Please make sure the file exists in the current directory.")
        return
    
    # Load training metrics data
    try:
        with open(metrics_file, 'r') as f:
            metrics_data = json.load(f)
        print(f"Successfully loaded training metrics from: {metrics_file}")
        
        # Check data structure
        if 'training_metrics' not in metrics_data:
            print("Error: Invalid file format. Expected 'training_metrics' key in JSON.")
            return
        
        num_epochs = len(metrics_data['training_metrics'])
        print(f"Found training data for {num_epochs} epochs")
        
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON format in {metrics_file}")
        print(f"JSON Error: {e}")
        return
    except Exception as e:
        print(f"Error loading file: {e}")
        return
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate training metrics visualization
    output_path = os.path.join(output_dir, 'training_metrics_analysis.png')
    
    try:
        plot_training_metrics_four_subplots(metrics_data, output_path)
        print(f"\n✅ Training metrics visualization completed!")
        print(f"📊 Output saved to: {output_path}")
        
        # Display summary statistics
        training_metrics = metrics_data['training_metrics']
        final_metrics = training_metrics[-1]
        
        print(f"\n📈 Training Summary:")
        print(f"   • Total Epochs: {final_metrics['epoch']}")
        print(f"   • Final Training Loss: {final_metrics['training_loss']:.6f}")
        print(f"   • Final Validation Loss: {final_metrics['validation_loss']:.6f}")
        print(f"   • Final Validation MAE: {final_metrics['validation_mae']:.6f}")
        print(f"   • Final Validation RMSE: {final_metrics['validation_rmse']:.6f}")
        print(f"   • Final Validation MAPE: {final_metrics['validation_mape']}")
        
        # Find best performance epochs
        val_losses = [m['validation_loss'] for m in training_metrics]
        val_maes = [m['validation_mae'] for m in training_metrics]
        val_rmses = [m['validation_rmse'] for m in training_metrics]
        val_mapes = [float(m['validation_mape'].rstrip('%')) for m in training_metrics]
        
        best_val_loss_epoch = val_losses.index(min(val_losses)) + 1
        best_mae_epoch = val_maes.index(min(val_maes)) + 1
        best_rmse_epoch = val_rmses.index(min(val_rmses)) + 1
        best_mape_epoch = val_mapes.index(min(val_mapes)) + 1
        
        print(f"\n🏆 Best Performance Epochs:")
        print(f"   • Best Validation Loss: Epoch {best_val_loss_epoch} ({min(val_losses):.6f})")
        print(f"   • Best MAE: Epoch {best_mae_epoch} ({min(val_maes):.6f})")
        print(f"   • Best RMSE: Epoch {best_rmse_epoch} ({min(val_rmses):.6f})")
        print(f"   • Best MAPE: Epoch {best_mape_epoch} ({min(val_mapes):.2f}%)")
        
    except Exception as e:
        print(f"❌ Error generating visualization: {e}")
        return

if __name__ == '__main__':
    main() 