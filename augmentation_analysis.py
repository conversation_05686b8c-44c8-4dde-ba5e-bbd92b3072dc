# augmentation_analysis.py - 数据增强效果分析
import torch
import numpy as np
import matplotlib.pyplot as plt
import json
import os
from collections import defaultdict, Counter
from typing import List, Dict, Tuple
import seaborn as sns

class AugmentationAnalyzer:
    """数据增强效果分析器"""
    
    def __init__(self):
        self.original_data = []
        self.augmented_data = []
    
    def load_original_data(self, data_dir: str):
        """加载原始数据"""
        print("加载原始数据...")
        pt_files = [f for f in os.listdir(data_dir) if f.endswith('.pt')]
        
        for pt_file in pt_files[:10]:  # 分析前10个文件
            file_path = os.path.join(data_dir, pt_file)
            data = torch.load(file_path, weights_only=True)
            self.original_data.append(data)
    
    def load_augmented_data(self, data_dir: str):
        """加载增强数据"""
        print("加载增强数据...")
        pt_files = [f for f in os.listdir(data_dir) if f.endswith('.pt')]
        
        for pt_file in pt_files[:30]:  # 分析前30个文件（对应10个原始文件的3倍增强）
            file_path = os.path.join(data_dir, pt_file)
            data = torch.load(file_path, weights_only=True)
            self.augmented_data.append(data)
    
    def analyze_time_distribution(self) -> Dict:
        """分析时间分布变化"""
        print("\n=== 时间分布分析 ===")
        
        # 原始数据时间分析
        original_times = []
        for data in self.original_data:
            original_times.extend(data[:, 0].tolist())
        
        # 增强数据时间分析
        augmented_times = []
        for data in self.augmented_data:
            augmented_times.extend(data[:, 0].tolist())
        
        original_times = np.array(original_times)
        augmented_times = np.array(augmented_times)
        
        analysis = {
            'original': {
                'count': len(original_times),
                'unique_times': len(np.unique(original_times)),
                'time_range': (original_times.min(), original_times.max()),
                'time_std': original_times.std()
            },
            'augmented': {
                'count': len(augmented_times),
                'unique_times': len(np.unique(augmented_times)),
                'time_range': (augmented_times.min(), augmented_times.max()),
                'time_std': augmented_times.std()
            }
        }
        
        print(f"原始数据:")
        print(f"  样本数: {analysis['original']['count']}")
        print(f"  唯一时间点: {analysis['original']['unique_times']}")
        print(f"  时间范围: [{analysis['original']['time_range'][0]:.6f}, {analysis['original']['time_range'][1]:.6f}]")
        print(f"  时间标准差: {analysis['original']['time_std']:.6f}")
        
        print(f"\n增强数据:")
        print(f"  样本数: {analysis['augmented']['count']}")
        print(f"  唯一时间点: {analysis['augmented']['unique_times']}")
        print(f"  时间范围: [{analysis['augmented']['time_range'][0]:.6f}, {analysis['augmented']['time_range'][1]:.6f}]")
        print(f"  时间标准差: {analysis['augmented']['time_std']:.6f}")
        
        # 计算时间多样性提升
        diversity_improvement = analysis['augmented']['unique_times'] / analysis['original']['unique_times']
        print(f"\n时间多样性提升: {diversity_improvement:.1f}倍")
        
        return analysis
    
    def analyze_flow_size_distribution(self) -> Dict:
        """分析流大小分布变化"""
        print("\n=== 流大小分布分析 ===")
        
        # 原始数据流大小分析（需要反标准化）
        original_norm_sizes = []
        for data in self.original_data:
            original_norm_sizes.extend(data[:, 2].tolist())
        
        # 增强数据流大小分析
        augmented_norm_sizes = []
        for data in self.augmented_data:
            augmented_norm_sizes.extend(data[:, 2].tolist())
        
        original_norm_sizes = np.array(original_norm_sizes)
        augmented_norm_sizes = np.array(augmented_norm_sizes)
        
        analysis = {
            'original': {
                'count': len(original_norm_sizes),
                'mean': original_norm_sizes.mean(),
                'std': original_norm_sizes.std(),
                'range': (original_norm_sizes.min(), original_norm_sizes.max())
            },
            'augmented': {
                'count': len(augmented_norm_sizes),
                'mean': augmented_norm_sizes.mean(),
                'std': augmented_norm_sizes.std(),
                'range': (augmented_norm_sizes.min(), augmented_norm_sizes.max())
            }
        }
        
        print(f"原始标准化流大小:")
        print(f"  样本数: {analysis['original']['count']}")
        print(f"  均值: {analysis['original']['mean']:.6f}")
        print(f"  标准差: {analysis['original']['std']:.6f}")
        print(f"  范围: [{analysis['original']['range'][0]:.6f}, {analysis['original']['range'][1]:.6f}]")
        
        print(f"\n增强标准化流大小:")
        print(f"  样本数: {analysis['augmented']['count']}")
        print(f"  均值: {analysis['augmented']['mean']:.6f}")
        print(f"  标准差: {analysis['augmented']['std']:.6f}")
        print(f"  范围: [{analysis['augmented']['range'][0]:.6f}, {analysis['augmented']['range'][1]:.6f}]")
        
        # 计算分布变化
        std_increase = analysis['augmented']['std'] / analysis['original']['std']
        print(f"\n流大小分布扩展: {std_increase:.2f}倍")
        
        return analysis
    
    def analyze_synchronous_contention(self) -> Dict:
        """分析同步拥塞特征变化"""
        print("\n=== 同步拥塞特征分析 ===")
        
        # 原始数据同步拥塞分析
        original_sync_counts = []
        original_sync_volumes = []
        for data in self.original_data:
            original_sync_counts.extend(data[:, 8].tolist())
            original_sync_volumes.extend(data[:, 9].tolist())
        
        # 增强数据同步拥塞分析
        augmented_sync_counts = []
        augmented_sync_volumes = []
        for data in self.augmented_data:
            augmented_sync_counts.extend(data[:, 8].tolist())
            augmented_sync_volumes.extend(data[:, 9].tolist())
        
        original_sync_counts = np.array(original_sync_counts)
        original_sync_volumes = np.array(original_sync_volumes)
        augmented_sync_counts = np.array(augmented_sync_counts)
        augmented_sync_volumes = np.array(augmented_sync_volumes)
        
        analysis = {
            'original': {
                'sync_count_mean': original_sync_counts.mean(),
                'sync_count_max': original_sync_counts.max(),
                'sync_volume_mean': original_sync_volumes.mean(),
                'sync_volume_max': original_sync_volumes.max(),
                'contention_ratio': (original_sync_counts > 0).mean()
            },
            'augmented': {
                'sync_count_mean': augmented_sync_counts.mean(),
                'sync_count_max': augmented_sync_counts.max(),
                'sync_volume_mean': augmented_sync_volumes.mean(),
                'sync_volume_max': augmented_sync_volumes.max(),
                'contention_ratio': (augmented_sync_counts > 0).mean()
            }
        }
        
        print(f"原始同步拥塞:")
        print(f"  平均拥塞流数: {analysis['original']['sync_count_mean']:.2f}")
        print(f"  最大拥塞流数: {analysis['original']['sync_count_max']:.0f}")
        print(f"  平均拥塞流量: {analysis['original']['sync_volume_mean']:.4f}")
        print(f"  存在拥塞比例: {analysis['original']['contention_ratio']:.2%}")
        
        print(f"\n增强同步拥塞:")
        print(f"  平均拥塞流数: {analysis['augmented']['sync_count_mean']:.2f}")
        print(f"  最大拥塞流数: {analysis['augmented']['sync_count_max']:.0f}")
        print(f"  平均拥塞流量: {analysis['augmented']['sync_volume_mean']:.4f}")
        print(f"  存在拥塞比例: {analysis['augmented']['contention_ratio']:.2%}")
        
        return analysis
    
    def estimate_training_improvement(self, time_analysis: Dict, 
                                    size_analysis: Dict, 
                                    contention_analysis: Dict) -> Dict:
        """估算训练改进效果"""
        print("\n=== 预期训练改进估算 ===")
        
        # 数据量增加
        data_increase = size_analysis['augmented']['count'] / size_analysis['original']['count']
        
        # 时间多样性增加
        time_diversity = time_analysis['augmented']['unique_times'] / time_analysis['original']['unique_times']
        
        # 流大小分布扩展
        size_diversity = size_analysis['augmented']['std'] / size_analysis['original']['std']
        
        # 拥塞模式变化
        contention_change = abs(contention_analysis['augmented']['contention_ratio'] - 
                              contention_analysis['original']['contention_ratio'])
        
        # 综合改进评估
        improvements = {
            'data_volume_increase': data_increase,
            'time_diversity_increase': time_diversity,
            'size_diversity_increase': size_diversity,
            'contention_pattern_change': contention_change,
            'expected_mae_improvement': min(0.3, 0.1 * time_diversity + 0.05 * size_diversity),
            'expected_overfitting_reduction': min(0.5, 0.2 * time_diversity),
            'expected_training_epochs': max(15, 10 * time_diversity)
        }
        
        print(f"数据量增加: {improvements['data_volume_increase']:.1f}倍")
        print(f"时间多样性增加: {improvements['time_diversity_increase']:.1f}倍")
        print(f"流大小多样性增加: {improvements['size_diversity_increase']:.2f}倍")
        print(f"拥塞模式变化: {improvements['contention_pattern_change']:.2%}")
        
        print(f"\n预期改进:")
        print(f"  MAE改进: {improvements['expected_mae_improvement']:.1%}")
        print(f"  过拟合缓解: {improvements['expected_overfitting_reduction']:.1%}")
        print(f"  可稳定训练轮次: {improvements['expected_training_epochs']:.0f}")
        
        return improvements
    
    def generate_visualization(self, save_dir: str = "visualizations"):
        """生成可视化图表"""
        os.makedirs(save_dir, exist_ok=True)
        
        # 1. 时间分布对比
        plt.figure(figsize=(12, 4))
        
        plt.subplot(1, 2, 1)
        original_times = []
        for data in self.original_data:
            original_times.extend(data[:, 0].tolist())
        plt.hist(original_times, bins=30, alpha=0.7, label='Original', color='blue')
        plt.xlabel('Start Time')
        plt.ylabel('Frequency')
        plt.title('Original Time Distribution')
        plt.legend()
        
        plt.subplot(1, 2, 2)
        augmented_times = []
        for data in self.augmented_data:
            augmented_times.extend(data[:, 0].tolist())
        plt.hist(augmented_times, bins=50, alpha=0.7, label='Augmented', color='red')
        plt.xlabel('Start Time')
        plt.ylabel('Frequency')
        plt.title('Augmented Time Distribution')
        plt.legend()
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'time_distribution_comparison.png'), dpi=300)
        plt.close()
        
        # 2. 流大小分布对比
        plt.figure(figsize=(12, 4))
        
        plt.subplot(1, 2, 1)
        original_sizes = []
        for data in self.original_data:
            original_sizes.extend(data[:, 2].tolist())
        plt.hist(original_sizes, bins=30, alpha=0.7, label='Original', color='blue')
        plt.xlabel('Normalized Flow Size')
        plt.ylabel('Frequency')
        plt.title('Original Flow Size Distribution')
        plt.legend()
        
        plt.subplot(1, 2, 2)
        augmented_sizes = []
        for data in self.augmented_data:
            augmented_sizes.extend(data[:, 2].tolist())
        plt.hist(augmented_sizes, bins=50, alpha=0.7, label='Augmented', color='red')
        plt.xlabel('Normalized Flow Size')
        plt.ylabel('Frequency')
        plt.title('Augmented Flow Size Distribution')
        plt.legend()
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'flow_size_distribution_comparison.png'), dpi=300)
        plt.close()
        
        print(f"可视化图表已保存到 {save_dir}/")

def main():
    analyzer = AugmentationAnalyzer()
    
    # 加载数据（需要先运行数据增强）
    try:
        analyzer.load_original_data('datasets_processed/train')
        analyzer.load_augmented_data('datasets_augmented/train')
    except FileNotFoundError as e:
        print(f"数据文件未找到: {e}")
        print("请先运行数据增强脚本生成增强数据")
        return
    
    # 执行分析
    time_analysis = analyzer.analyze_time_distribution()
    size_analysis = analyzer.analyze_flow_size_distribution()
    contention_analysis = analyzer.analyze_synchronous_contention()
    
    # 估算改进效果
    improvements = analyzer.estimate_training_improvement(
        time_analysis, size_analysis, contention_analysis
    )
    
    # 生成可视化
    analyzer.generate_visualization()
    
    print("\n=== 分析完成 ===")
    print("建议:")
    print("1. 使用增强数据重新训练模型")
    print("2. 适当增加训练轮次到15-25轮")
    print("3. 可以考虑稍微降低学习率以适应更多样的数据")

if __name__ == '__main__':
    main()
