# data_augmentation.py - 时间扰动和流量大小扰动增强
import torch
import numpy as np
import json
import os
import random
from typing import List, Dict, Tuple
from collections import defaultdict
from tqdm import tqdm
import argparse

from clos_topo import Clos
from preprocess import LinkGraphBuilder

class DataAugmentationEngine:
    """数据增强引擎：实现时间扰动和流量大小扰动"""
    
    def __init__(self, clos_topology: Clos, global_mean: float, global_std: float):
        self.clos = clos_topology
        self.link_builder = LinkGraphBuilder(clos_topology)
        self.global_mean = global_mean
        self.global_std = global_std
        
        # 时间扰动参数
        self.time_perturbation_std = 0.002  # 标准差2ms
        self.time_perturbation_max = 0.005  # 最大扰动5ms
        
        # 流量扰动参数
        self.flow_size_noise_ratio = 0.1    # 10%的相对噪声
        self.flow_size_min_factor = 0.8     # 最小80%原值
        self.flow_size_max_factor = 1.2     # 最大120%原值
        
    def time_perturbation_augmentation(self, original_flows: List[Dict], 
                                     augmentation_factor: int = 3) -> List[List[Dict]]:
        """
        时间扰动增强：为每个原始批次生成多个时间扰动版本
        
        Args:
            original_flows: 原始流列表
            augmentation_factor: 增强倍数
            
        Returns:
            增强后的流列表的列表
        """
        augmented_batches = []
        
        for aug_idx in range(augmentation_factor):
            # 为每个增强版本设置不同的随机种子
            np.random.seed(42 + aug_idx * 1000)
            
            augmented_flows = []
            
            # 按开始时间分组
            flows_by_time = defaultdict(list)
            for flow in original_flows:
                start_time = flow['inputs']['start_time']
                flows_by_time[start_time].append(flow)
            
            # 对每个时间组应用扰动
            time_offset = 0.0
            for time_point in sorted(flows_by_time.keys()):
                flows_at_time = flows_by_time[time_point]
                
                # 生成时间扰动
                if aug_idx == 0:
                    # 第一个增强版本：轻微扰动
                    perturbations = np.random.normal(0, self.time_perturbation_std/2, len(flows_at_time))
                elif aug_idx == 1:
                    # 第二个增强版本：中等扰动
                    perturbations = np.random.normal(0, self.time_perturbation_std, len(flows_at_time))
                else:
                    # 其他版本：较大扰动
                    perturbations = np.random.normal(0, self.time_perturbation_std*1.5, len(flows_at_time))
                
                # 限制扰动范围
                perturbations = np.clip(perturbations, -self.time_perturbation_max, self.time_perturbation_max)
                
                # 应用扰动并创建新流
                for i, flow in enumerate(flows_at_time):
                    new_flow = self._deep_copy_flow(flow)
                    
                    # 应用时间扰动
                    original_start = flow['inputs']['start_time']
                    perturbed_start = max(0.0, original_start + perturbations[i] + time_offset)
                    new_flow['inputs']['start_time'] = perturbed_start
                    
                    # 更新flow_id以区分增强样本
                    original_id = flow['inputs']['flow_id']
                    new_flow['inputs']['flow_id'] = f"{original_id}_aug{aug_idx}_t{i}"
                    
                    augmented_flows.append(new_flow)
                
                # 累积时间偏移，保持时间顺序
                time_offset += np.random.uniform(0, 0.001)
            
            # 按新的开始时间重新排序
            augmented_flows.sort(key=lambda x: x['inputs']['start_time'])
            augmented_batches.append(augmented_flows)
        
        return augmented_batches
    
    def flow_size_perturbation_augmentation(self, flows: List[Dict], 
                                          augmentation_factor: int = 2) -> List[List[Dict]]:
        """
        流量大小扰动增强
        
        Args:
            flows: 输入流列表
            augmentation_factor: 增强倍数
            
        Returns:
            增强后的流列表的列表
        """
        augmented_batches = []
        
        for aug_idx in range(augmentation_factor):
            np.random.seed(42 + aug_idx * 2000)
            
            augmented_flows = []
            
            for flow in flows:
                new_flow = self._deep_copy_flow(flow)
                original_size = flow['inputs']['flow_features'][0]
                
                if aug_idx == 0:
                    # 第一个版本：加性高斯噪声
                    noise = np.random.normal(0, original_size * self.flow_size_noise_ratio)
                    new_size = original_size + noise
                else:
                    # 第二个版本：乘性噪声
                    multiplier = np.random.uniform(self.flow_size_min_factor, self.flow_size_max_factor)
                    new_size = original_size * multiplier
                
                # 确保流大小为正数且在合理范围内
                new_size = max(1.0, new_size)  # 最小1字节
                new_size = min(new_size, original_size * 3)  # 最大3倍原值
                
                new_flow['inputs']['flow_features'][0] = new_size
                
                # 更新flow_id
                original_id = flow['inputs']['flow_id']
                new_flow['inputs']['flow_id'] = f"{original_id}_aug_size{aug_idx}"
                
                augmented_flows.append(new_flow)
            
            augmented_batches.append(augmented_flows)
        
        return augmented_batches
    
    def combined_augmentation(self, original_flows: List[Dict], 
                            time_factor: int = 3, 
                            size_factor: int = 2) -> List[List[Dict]]:
        """
        组合增强：同时应用时间扰动和流量大小扰动
        
        Args:
            original_flows: 原始流列表
            time_factor: 时间扰动倍数
            size_factor: 流量扰动倍数
            
        Returns:
            增强后的流列表的列表
        """
        all_augmented = []
        
        # 1. 首先应用时间扰动
        time_augmented_batches = self.time_perturbation_augmentation(original_flows, time_factor)
        
        # 2. 对每个时间扰动版本应用流量扰动
        for time_batch in time_augmented_batches:
            size_augmented_batches = self.flow_size_perturbation_augmentation(time_batch, size_factor)
            all_augmented.extend(size_augmented_batches)
        
        return all_augmented
    
    def _deep_copy_flow(self, flow: Dict) -> Dict:
        """深拷贝流数据"""
        return {
            'inputs': {
                'flow_id': flow['inputs']['flow_id'],
                'start_time': flow['inputs']['start_time'],
                'path': flow['inputs']['path'].copy(),
                'flow_features': flow['inputs']['flow_features'].copy()
            },
            'time_delay': flow['time_delay']
        }
    
    def process_and_save_augmented_data(self, augmented_flows: List[Dict], 
                                      output_path: str) -> torch.Tensor:
        """
        处理增强数据并保存为tensor格式
        
        Args:
            augmented_flows: 增强后的流列表
            output_path: 输出路径
            
        Returns:
            处理后的tensor
        """
        # 按开始时间对所有流进行分组（重新计算同步拥塞特征）
        flows_by_start_time = defaultdict(list)
        for flow in augmented_flows:
            path_links = self.link_builder.get_path_link_indices(flow['inputs']['path'])
            if path_links is None or len(path_links) != 4:
                continue
            
            start_time = flow['inputs']['start_time']
            flow_size = flow['inputs']['flow_features'][0]
            norm_size = (flow_size - self.global_mean) / self.global_std
            
            flows_by_start_time[start_time].append({
                'original_flow': flow,
                'path_links': path_links,
                'path_links_set': set(path_links),
                'norm_size': norm_size
            })
        
        processed_flow_info_list = []
        
        # 重新计算同步拥塞特征
        for start_time, sync_flows_group in flows_by_start_time.items():
            for i, target_flow in enumerate(sync_flows_group):
                max_sync_count = 0
                max_sync_volume = 0.0
                
                # 计算与其他同步流的拥塞
                for link_idx in target_flow['path_links']:
                    current_link_sync_count = 0
                    current_link_sync_volume = 0.0
                    
                    for j, other_flow in enumerate(sync_flows_group):
                        if i == j:
                            continue
                        
                        if link_idx in other_flow['path_links_set']:
                            current_link_sync_count += 1
                            current_link_sync_volume += other_flow['norm_size']
                    
                    max_sync_count = max(max_sync_count, current_link_sync_count)
                    max_sync_volume = max(max_sync_volume, current_link_sync_volume)
                
                # 创建特征向量
                original_flow = target_flow['original_flow']
                start_time = original_flow['inputs']['start_time']
                time_delay = original_flow['time_delay']
                end_time = start_time + time_delay
                
                info_vector = [
                    start_time,
                    end_time,
                    target_flow['norm_size'],
                    *target_flow['path_links'],
                    time_delay,
                    float(max_sync_count),
                    float(max_sync_volume)
                ]
                processed_flow_info_list.append(info_vector)
        
        if not processed_flow_info_list:
            return torch.empty(0, 10)
        
        # 按开始时间排序并转换为tensor
        processed_flow_info_list.sort(key=lambda x: x[0])
        flows_tensor = torch.tensor(processed_flow_info_list, dtype=torch.float32)
        
        # 保存tensor
        torch.save(flows_tensor, output_path)
        
        return flows_tensor

def augment_dataset(input_dir: str, output_dir: str, 
                   time_factor: int = 3, size_factor: int = 2,
                   global_mean: float = 572.8143, global_std: float = 388.6392):
    """
    对整个数据集进行增强
    
    Args:
        input_dir: 输入目录（原始JSON文件）
        output_dir: 输出目录（增强后的tensor文件）
        time_factor: 时间扰动倍数
        size_factor: 流量扰动倍数
        global_mean: 全局均值
        global_std: 全局标准差
    """
    # 初始化
    clos = Clos(4, 4, 4, ps_bandwidth=16000, sh_bandwidth=16000)
    clos.build()
    augmenter = DataAugmentationEngine(clos, global_mean, global_std)
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取所有JSON文件
    json_files = [f for f in os.listdir(input_dir) if f.endswith('.json')]
    
    print(f"开始增强数据集，共{len(json_files)}个文件")
    print(f"时间扰动倍数: {time_factor}, 流量扰动倍数: {size_factor}")
    print(f"预期总增强倍数: {time_factor * size_factor}")
    
    total_original_samples = 0
    total_augmented_samples = 0
    
    for json_file in tqdm(json_files, desc="增强数据"):
        input_path = os.path.join(input_dir, json_file)
        
        try:
            # 加载原始数据
            with open(input_path, 'r') as f:
                data = json.load(f)
            
            original_flows = data.get('flows', [])
            total_original_samples += len(original_flows)
            
            # 应用组合增强
            augmented_batches = augmenter.combined_augmentation(
                original_flows, time_factor, size_factor
            )
            
            # 保存每个增强版本
            base_name = json_file.replace('.json', '')
            for aug_idx, augmented_flows in enumerate(augmented_batches):
                output_filename = f"{base_name}_aug{aug_idx}.pt"
                output_path = os.path.join(output_dir, output_filename)
                
                tensor_data = augmenter.process_and_save_augmented_data(
                    augmented_flows, output_path
                )
                total_augmented_samples += tensor_data.shape[0]
        
        except Exception as e:
            print(f"处理文件 {json_file} 时出错: {e}")
            continue
    
    print(f"\n增强完成!")
    print(f"原始样本数: {total_original_samples}")
    print(f"增强样本数: {total_augmented_samples}")
    print(f"实际增强倍数: {total_augmented_samples / total_original_samples:.1f}")

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='数据增强脚本')
    parser.add_argument('--input_dir', type=str, default='data_gen/datasets/train', 
                       help='输入目录（原始JSON文件）')
    parser.add_argument('--output_dir', type=str, default='datasets_augmented/train', 
                       help='输出目录（增强tensor文件）')
    parser.add_argument('--time_factor', type=int, default=3, help='时间扰动倍数')
    parser.add_argument('--size_factor', type=int, default=2, help='流量扰动倍数')
    parser.add_argument('--global_mean', type=float, default=572.8143, help='全局均值')
    parser.add_argument('--global_std', type=float, default=388.6392, help='全局标准差')
    
    args = parser.parse_args()
    
    augment_dataset(
        args.input_dir, args.output_dir, 
        args.time_factor, args.size_factor,
        args.global_mean, args.global_std
    )
