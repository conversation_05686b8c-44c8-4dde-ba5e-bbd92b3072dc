DEFAULT_PARAMS = {
    # 批次数量配置
    "num_train": 200,
    "num_validation": 100,
    "num_test": 1,
    
    # 训练和验证集配置 - 修改为复杂场景配置
    "unified_config": {
        "rar_ops_per_batch": 12,                     # 4→12: 3倍复杂度，增加网络负载
        "rar_group_size_range": (2, 12),            # (1,16)→(2,12): 适中的节点组范围
        "time_between_rars_range": (0.001, 0.03),          # (0.001,0.05)→(0.001,0.03): 增加竞争密度
        "burst_probability": 0.8,               # 0.9→0.8: 高爆发概率但略微减少
        "rar_congestion_control": {
            "uplink_congestion_ratio": 0.9,      # 1.0→0.9: 引入轻微拥塞，适应容量限制
            "downlink_congestion_ratio": 0.9,    # 1.0→0.9: 平衡上下行拥塞
            "bottleneck_sharing_mode": "concentrated"  # 保持集中瓶颈共享模式
        }
    },
    
    # 测试集独立配置（更具挑战性的测试场景）
    "test_config": {
        "rar_ops_per_batch": 20,                     # 每批次包含5个CCG（更高负载）
        "rar_group_size_range": (1, 8),            # 更大的节点组范围
        "time_between_rars_range": (0.001, 0.05),          # 更短的正常间隔（更高并发）
        "burst_probability": 0.7,                # 更高的爆发概率（更极端场景）
        "rar_congestion_control": {
            "uplink_congestion_ratio": 0.8,      # 更高的上行拥塞率
            "downlink_congestion_ratio": 0.8,    # 更高的下行拥塞率  
            "bottleneck_sharing_mode": "concentrated"
        }
    },
    

    
    # 全局配置
    "output_base_dir": "data_gen/datasets",
    "temp_dir": "dataset_temp",
    
    # 拓扑配置
    "topology": {
        "pods": 4,
        "switches_per_pod": 4,
        "hosts_per_switch": 4,
        "ps_bandwidth": 16000,
        "sh_bandwidth": 16000
    },
    
    # 仿真配置
    "simulation": {
        "timestep": 0.0001,
        "max_time": 500.0
    }
}

