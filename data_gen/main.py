import os
import json
import random
import argparse
import shutil
from tqdm import tqdm
import time
from itertools import cycle
import math

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from clos_topo import Clos
from data_gen.constants import DL_WORKLOADS
from data_gen.config import DEFAULT_PARAMS
from data_gen.flow_simulator import PathManager, TimeBasedFlowScheduler



class FlowTemplateFactory:
    """生成'集合通信组' (CCG) 模板, 例如 Ring All-Reduce。"""
    
    def __init__(self, path_manager, unified_config, batch_id):
        self.path_manager = path_manager
        self.config = unified_config
        self.batch_id = batch_id
        self.flow_counter = 0
        self.all_hosts = [h for pod_hosts in self.path_manager.pod_hosts.values() for h in pod_hosts]

    def _create_one_ccg_template(self, ccg_id):
        """为单个CCG创建一组流模板。"""
        # 1. 选择 k 个节点
        k = random.randint(*self.config['rar_group_size_range'])
        if len(self.all_hosts) < k:
            print(f"警告: 主机数量 ({len(self.all_hosts)}) 不足以创建大小为 {k} 的RAR组。")
            return None
        
        selected_hosts = random.sample(self.all_hosts, k)
        random.shuffle(selected_hosts) # 随机化环的顺序

        # 2. 创建环形通信流
        ccg_flows = []
        # 修正逻辑：先选择模型，然后基于模型参数计算流大小
        workload = random.choice(DL_WORKLOADS)
        # 将参数个数转换为梯度数据大小(MB): 参数个数 × 4字节 ÷ (1024²)
        flow_size = (workload["params"] * 4) / (1024 * 1024)
        
        # 验证流大小计算正确性
        expected_sizes = {
            "ResNet-50": 97.66,     # 25.6M × 4 / 1024² ≈ 97.66MB
            "BERT-Large": 1296.0,   # 340M × 4 / 1024² ≈ 1296MB
            "DLRM": 495.91,         # 130M × 4 / 1024² ≈ 495.91MB
            "VGG-16": 526.43,       # 138M × 4 / 1024² ≈ 526.43MB
            "DeepSpeech2": 457.76   # 120M × 4 / 1024² ≈ 457.76MB
        }
        expected = expected_sizes[workload["model"]]
        assert abs(flow_size - expected) < 1.0, f"流大小计算错误: {flow_size:.2f}MB vs 期望{expected}MB"

        host_cycle = cycle(selected_hosts)
        source_host = next(host_cycle)
        
        for i in range(k):
            target_host = next(host_cycle)
            
            path = self.path_manager.get_cross_pod_path(source_host, target_host)
            if not path: # 确保路径存在
                source_host = target_host
                continue

            flow_template = {
                "flow_id": f"{self.batch_id}_f{self.flow_counter}",
                "ccg_id": f"{self.batch_id}_{ccg_id}",
                "rar_group_size": k,
                "source": source_host,
                "target": target_host,
                "path": path,
                "flow_features": [flow_size],
                "workload": {
                    "model": workload["model"],
                    "dataset": workload["dataset"],
                    "parameters": workload.get("params", 0)
                }
            }
            ccg_flows.append(flow_template)
            self.flow_counter += 1
            source_host = target_host

        return ccg_flows

    def generate_ccg_pool(self):
        """为整个批次生成一个CCG模板池。"""
        pool = []
        num_rar_ops = self.config['rar_ops_per_batch']  # 固定为3
        
        for i in range(num_rar_ops):
            ccg_template = self._create_one_ccg_template(f"ccg{i}")
            if ccg_template:
                pool.append(ccg_template)
        
        return pool


class EventScheduler:
    """在时间轴上调度CCG，为每个组的所有流分配相同的start_time。"""

    def __init__(self, unified_config):
        self.config = unified_config
        self.current_time = 0.0

    def schedule_flows(self, ccg_pool):
        """为一批CCG分配启动时间。"""
        scheduled_flows = []
        
        for ccg_group in ccg_pool:
            # 为组内所有流分配相同的开始时间
            for flow in ccg_group:
                flow['start_time'] = self.current_time
            scheduled_flows.extend(ccg_group)

            # 推进时间，并考虑爆发情况
            if random.random() < self.config['burst_probability']:
                # 爆发：高并发短间隔
                self.current_time += random.uniform(0.001,0.005)
            else:
                # 正常间隔：低并发长间隔
                self.current_time += random.uniform(*self.config['time_between_rars_range'])
            
        return scheduled_flows


def _simulate_single_batch(clos, scheduled_flows, batch_id):
    """仿真单个批次并获取延迟数据。"""
    try:
        all_tasks = []
        for flow in scheduled_flows:
            task = {
                "task_id": flow["flow_id"],
                "source": flow["source"],
                "target": flow["target"],
                "gradient_size_mb": flow["flow_features"][0],
                "comm_start_time": flow["start_time"],
                "paths": [flow["path"]]
            }
            all_tasks.append(task)

        if not all_tasks:
            return None

        sim_params = DEFAULT_PARAMS['simulation']
        scheduler = TimeBasedFlowScheduler(
            clos,
            tasks_data=all_tasks,
            timestep=sim_params['timestep'],
            max_time=sim_params['max_time']
        )
        
        simulation_results = scheduler.simulate_time_based()
        
        batch_data = {"batch_id": batch_id, "flows": []}
        
        for flow in scheduled_flows:
            flow_id = flow["flow_id"]
            result = simulation_results.get(flow_id, {"fct": -1})
            
            if result["fct"] > 0:
                flow_data = {
                    "inputs": {
                        "flow_id": flow_id,
                        "ccg_id": flow["ccg_id"],
                        "rar_group_size": flow["rar_group_size"],
                        "flow_features": flow["flow_features"],
                        "path": flow["path"],
                        "start_time": flow["start_time"],
                        "model": flow["workload"]["model"],
                        "dataset": flow["workload"]["dataset"],
                        "parameters": flow["workload"]["parameters"],
                    },
                    "time_delay": result["fct"]
                }
                batch_data["flows"].append(flow_data)

        if not batch_data["flows"]:
            print(f"警告: 批次 {batch_id} 没有成功完成任何流，将跳过。")
            return None
        
        return batch_data
        
    except Exception as e:
        print(f"仿真批次 {batch_id} 时出错: {e}")
        import traceback
        traceback.print_exc()
        return None


def simple_random_split(file_list, num_train, num_validation, num_test):
    """简单随机划分批次文件到训练、验证、测试集"""
    total_files = len(file_list)
    total_needed = num_train + num_validation + num_test
    
    if total_needed > total_files:
        print(f"警告: 需要 {total_needed} 个文件，但只生成了 {total_files} 个")
        # 按比例调整
        ratio = total_files / total_needed
        num_train = int(num_train * ratio)
        num_validation = int(num_validation * ratio)
        num_test = total_files - num_train - num_validation
        print(f"调整后: train={num_train}, validation={num_validation}, test={num_test}")
    
    # 随机打乱文件列表
    shuffled_files = file_list.copy()
    random.shuffle(shuffled_files)
    
    # 按序分配
    train_files = shuffled_files[:num_train]
    val_files = shuffled_files[num_train:num_train + num_validation]
    test_files = shuffled_files[num_train + num_validation:num_train + num_validation + num_test]
    
    print(f"随机划分完成:")
    print(f"  训练集: {len(train_files)} 个批次")
    print(f"  验证集: {len(val_files)} 个批次")
    print(f"  测试集: {len(test_files)} 个批次")
    
    return {
        "train": train_files,
        "validation": val_files,
        "test": test_files
    }


def move_files_to_final_dirs(temp_dir, splits_dict, output_base_dir):
    """移动文件到最终目录结构"""
    os.makedirs(output_base_dir, exist_ok=True)
    
    for split_name, file_list in splits_dict.items():
        split_dir = os.path.join(output_base_dir, split_name)
        os.makedirs(split_dir, exist_ok=True)
        
        for i, filename in enumerate(file_list):
            src_path = os.path.join(temp_dir, filename)
            dst_path = os.path.join(split_dir, f"batch_{i}.json")
            
            if os.path.exists(src_path):
                shutil.copy2(src_path, dst_path)
        
        print(f"移动 {split_name} 数据集: {len(file_list)} 个批次")


def generate_dataset(params):
    """主函数，生成并划分整个数据集。"""
    
    # --- 1. 初始化 ---
    output_base_dir = params["output_base_dir"]
    temp_dir = params["temp_dir"]
    if os.path.exists(output_base_dir):
        shutil.rmtree(output_base_dir)
    if os.path.exists(temp_dir):
        shutil.rmtree(temp_dir)
    os.makedirs(temp_dir, exist_ok=True)

    train_val_batches = params["num_train"] + params["num_validation"]
    test_batches = params["num_test"]
    total_batches = train_val_batches + test_batches
    
    unified_config = params["unified_config"]
    test_config = params["test_config"]
    
    print("=== 独立配置数据生成计划 ===")
    print(f"总批次数: {total_batches} (训练+验证: {train_val_batches}, 测试: {test_batches})")
    print("\n训练+验证集配置:")
    print(f"  每批次CCG数量: {unified_config['rar_ops_per_batch']}")
    print(f"  CCG组大小范围: {unified_config['rar_group_size_range']}")
    print(f"  流大小: 基于选定模型的参数量计算")
    print(f"  时间间隔范围: {unified_config['time_between_rars_range']}")
    print(f"  爆发概率: {unified_config['burst_probability']}")
    print("\n测试集配置:")
    print(f"  每批次CCG数量: {test_config['rar_ops_per_batch']}")
    print(f"  CCG组大小范围: {test_config['rar_group_size_range']}")
    print(f"  流大小: 基于选定模型的参数量计算")
    print(f"  时间间隔范围: {test_config['time_between_rars_range']}")
    print(f"  爆发概率: {test_config['burst_probability']}")
    print("="*40)

    # --- 2. 分组生成批次 ---
    print("=== 步骤 1/3: 分组生成批次 ===")
    
    topo_params = params['topology']
    clos = Clos(
        topo_params['pods'], 
        topo_params['switches_per_pod'], 
        topo_params['hosts_per_switch'], 
        ps_bandwidth=topo_params['ps_bandwidth'], 
        sh_bandwidth=topo_params['sh_bandwidth']
    )
    clos.build()

    # 生成训练+验证批次
    print(f"生成训练+验证批次 ({train_val_batches} 个)...")
    train_val_files = []
    batch_counter = 0
    
    for _ in tqdm(range(train_val_batches), desc="Generating train+val batches"):
        batch_id = f"batch_{batch_counter}"
        path_manager = PathManager(clos, unified_config.get('rar_congestion_control', {}))

        # 使用训练验证配置
        factory = FlowTemplateFactory(path_manager, unified_config, batch_id)
        ccg_pool = factory.generate_ccg_pool()
        
        scheduler = EventScheduler(unified_config)
        scheduled_flows = scheduler.schedule_flows(ccg_pool)
        
        batch_data = _simulate_single_batch(clos, scheduled_flows, batch_id)

        if batch_data:
            filename = f"{batch_id}.json"
            filepath = os.path.join(temp_dir, filename)
            with open(filepath, 'w') as f:
                json.dump(batch_data, f, indent=4)
            
            train_val_files.append(filename)
            batch_counter += 1

    # 生成测试批次
    print(f"\n生成测试批次 ({test_batches} 个)...")
    test_files = []
    
    for _ in tqdm(range(test_batches), desc="Generating test batches"):
        batch_id = f"batch_{batch_counter}"
        path_manager = PathManager(clos, test_config.get('rar_congestion_control', {}))

        # 使用测试配置
        factory = FlowTemplateFactory(path_manager, test_config, batch_id)
        ccg_pool = factory.generate_ccg_pool()
        
        scheduler = EventScheduler(test_config)
        scheduled_flows = scheduler.schedule_flows(ccg_pool)
        
        batch_data = _simulate_single_batch(clos, scheduled_flows, batch_id)

        if batch_data:
            filename = f"{batch_id}.json"
            filepath = os.path.join(temp_dir, filename)
            with open(filepath, 'w') as f:
                json.dump(batch_data, f, indent=4)
            
            test_files.append(filename)
            batch_counter += 1

    total_generated = len(train_val_files) + len(test_files)
    if not total_generated:
        print("错误：未能生成任何批次文件，程序终止。")
        return

    print(f"\n分组生成完成:")
    print(f"  训练+验证: {len(train_val_files)} 个批次")
    print(f"  测试: {len(test_files)} 个批次")
    print(f"  总计: {total_generated} 个批次")

    # --- 3. 分组划分批次 ---
    print("\n=== 步骤 2/3: 分组划分 ===")
    
    # 从训练+验证文件中划分训练集和验证集
    train_val_splits = simple_random_split(
        train_val_files,
        params['num_train'], 
        params['num_validation'], 
        0  # 不从这组中选择测试集
    )
    
    # 测试集直接使用测试文件
    splits_dict = {
        "train": train_val_splits["train"],
        "validation": train_val_splits["validation"],
        "test": test_files  # 直接使用测试批次
    }
    
    print("分组划分完成:")
    print(f"  训练集: {len(splits_dict['train'])} 个批次 (来自训练+验证组)")
    print(f"  验证集: {len(splits_dict['validation'])} 个批次 (来自训练+验证组)")
    print(f"  测试集: {len(splits_dict['test'])} 个批次 (独立配置)")

    # --- 4. 移动文件到最终目录 ---
    print("\n=== 步骤 3/3: 移动文件到最终目录 ===")
    move_files_to_final_dirs(temp_dir, splits_dict, output_base_dir)

    # --- 4. 清理临时文件 ---
    shutil.rmtree(temp_dir)
    print(f"\n已删除临时目录: {temp_dir}")

    # --- 5. 最终统计 ---
    total_flows = 0
    for split_name, file_list in splits_dict.items():
        split_dir = os.path.join(output_base_dir, split_name)
        split_flows = 0
        for i in range(len(file_list)):
            batch_file = os.path.join(split_dir, f"batch_{i}.json")
            if os.path.exists(batch_file):
                with open(batch_file, 'r') as f:
                    batch_data = json.load(f)
                split_flows += len(batch_data.get("flows", []))
        total_flows += split_flows
        print(f"{split_name.upper()}: {len(file_list)} 批次, {split_flows} 流")

    print(f"\n🎉 独立配置数据集生成完成！")
    print(f"总计: {total_generated} 批次, {total_flows} 流")
    print(f"  - 训练+验证集使用统一配置")
    print(f"  - 测试集使用独立配置 (更高负载和挑战性)")
    print(f"数据集已保存到: {output_base_dir}")


def main():
    parser = argparse.ArgumentParser(
        description="网络延迟预测数据集生成器 (V2 - RAR Model)",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument('--num-train', type=int, default=DEFAULT_PARAMS['num_train'], help='训练批次数量')
    parser.add_argument('--num-validation', type=int, default=DEFAULT_PARAMS['num_validation'], help='验证批次数量')
    parser.add_argument('--num-test', type=int, default=DEFAULT_PARAMS['num_test'], help='测试批次数量')
    parser.add_argument('--output-dir', type=str, default=DEFAULT_PARAMS['output_base_dir'], help='最终输出数据集的基础目录')

    args = parser.parse_args()

    num_train = args.num_train
    num_validation = args.num_validation
    num_test = args.num_test

    # 确保至少有足够的批次来满足最低场景分布
    total_min_batches = 3 
    if num_train + num_validation + num_test < total_min_batches:
        print(f"警告：总批次数小于{total_min_batches}，可能导致某些数据集为空。自动调整批次数...")
        num_train = max(1, num_train)
        num_validation = max(1, num_validation)
        num_test = max(1, num_test)
        print(f"调整后: train={num_train}, validation={num_validation}, test={num_test}")

    
    params = DEFAULT_PARAMS.copy()
    params.update({
        "num_train": num_train,
        "num_validation": num_validation,
        "num_test": num_test,
        "output_base_dir": args.output_dir
    })

    generate_dataset(params)


if __name__ == "__main__":
    main() 