#!/usr/bin/env python3

import json
import random
import numpy as np
import os
import networkx as nx
from collections import defaultdict
import matplotlib.pyplot as plt
from tqdm import tqdm
import time
import logging
import argparse
import abc
import yaml # 导入YAML库
from clos_topo import Clos


class TrafficPattern(abc.ABC):
    """流量模式的抽象基类 (策略接口)"""
    
    def __init__(self, config):
        self.config = config

    @abc.abstractmethod
    def generate_flows(self, batch_id, path_manager, flow_counter_start=0):
        """生成一个批次的流数据"""
        pass

    @staticmethod
    def create_flow_dict(flow_id, source, target, path, flow_size, start_time, workload, is_congested, is_burst, **kwargs):
        """通用的流创建辅助函数"""
        flow_data = {
            "flow_id": flow_id,
            "source": source,
            "target": target,
            "path": path,
            "flow_features": [flow_size],
            "start_time": start_time,
            "workload": {
                "model": workload["model"],
                "dataset": workload["dataset"],
                "parameters": workload.get("params", 0)
            },
            "is_congested": is_congested,
            "is_burst": is_burst
        }
        flow_data.update(kwargs)  # 用于添加 wave_index 等额外信息
        return flow_data


class NormalTraffic(TrafficPattern):
    """普通流量模式 (具体策略)"""
    
    def generate_flows(self, batch_id, path_manager, flow_counter_start=0):
        """生成普通模式的流数据"""
        flows = []
        flow_counter = flow_counter_start
        
        # 随机化批次参数
        num_flows = random.randint(*self.config['flows_per_batch_range'])
        
        # 选择目标Pod
        path_manager.select_target_pods(self.config.get('target_pods_count', 2))
        
        print(f"生成普通批次: {num_flows} 个流")
        
        # 生成流（不再区分拥塞流和正常流）
        successful_flows = 0
        for i in range(num_flows):
            source, target = path_manager.generate_normal_flow_pair()
            if source and target:
                path = path_manager.get_cross_pod_path(source, target)
                if path and len(path) == 5:  # 确保是4跳路径
                    flow = self._create_flow(f"{batch_id}_norm_flow_{flow_counter}", 
                                           source, target, path)
                    flows.append(flow)
                    successful_flows += 1
                    flow_counter += 1
        
        # 按照开始时间排序
        flows.sort(key=lambda x: x['start_time'])
        
        print(f"普通批次生成完成: 总共 {len(flows)} 个流")
        print(f"  -> 成功生成流: {successful_flows}/{num_flows}")
        
        return {
            "batch_id": batch_id,
            "flows": flows,
            "metadata": {
                "num_flows": len(flows),
                "is_burst": False,
                "target_pods": path_manager.target_pods,
            },
            "next_flow_counter": flow_counter
        }

    def _create_flow(self, flow_id, source, target, path):
        """创建单个流的数据结构"""
        flow_size = random.uniform(*self.config['flow_size_range'])
        start_time = random.uniform(*self.config['flow_arrival_interval_range'])
        workload = random.choice(DL_WORKLOADS)
        
        return TrafficPattern.create_flow_dict(
            flow_id, source, target, path, flow_size, start_time,
            workload, False, is_burst=False
        )


class BurstTraffic(TrafficPattern):
    """多波次爆发流量模式 (具体策略)"""
    
    def generate_flows(self, batch_id, path_manager, flow_counter_start=0):
        """生成爆发模式的流数据"""
        flows = []
        flow_counter = flow_counter_start
        
        # 选择目标Pod
        path_manager.select_target_pods(self.config['target_pods_count'])
        
        # 计算爆发参数
        num_flows = random.randint(*self.config['flow_count_range'])
        waves_count = random.randint(*self.config['waves_count_range'])
        
        # 计算波次分布和时间
        wave_flows_distribution = self._calculate_wave_distribution(num_flows, waves_count)
        wave_start_times = self._calculate_wave_start_times(waves_count)
        
        print(f"生成多波次爆发批次: {waves_count} 波，总流量 {num_flows} 个")
        
        total_flows_generated = 0
        
        # 生成每个波次的流
        for wave_idx in range(waves_count):
            wave_flows = wave_flows_distribution[wave_idx]
            wave_start_time = wave_start_times[wave_idx]
            
            print(f"  第 {wave_idx + 1} 波 (时间 {wave_start_time:.2f}s): {wave_flows} 个流")
            
            # 生成该波次的流（不再区分拥塞流和正常流）
            for i in range(wave_flows):
                flow_start_time = wave_start_time + random.uniform(*self.config['time_window'])
                flow_size = self._get_flow_size_by_ratio(i, wave_flows)
                
                source, target = path_manager.generate_normal_flow_pair()
                if source and target:
                    path = path_manager.get_cross_pod_path(source, target)
                    if path and len(path) == 5:  # 确保是4跳路径
                        flow = self._create_burst_flow(
                            f"{batch_id}_wave{wave_idx}_flow_{flow_counter}",
                            source, target, path, flow_size, flow_start_time,
                            is_burst=True, wave_index=wave_idx
                        )
                        flows.append(flow)
                        total_flows_generated += 1
                        flow_counter += 1
        
        # 按照开始时间排序
        flows.sort(key=lambda x: x['start_time'])
        
        print(f"多波次爆发批次生成完成: 总共 {len(flows)} 个流")
        print(f"  -> 总生成流: {total_flows_generated}")
        print(f"  -> 波次分布: {wave_flows_distribution}")
        
        return {
            "batch_id": batch_id,
            "flows": flows,
            "metadata": {
                "num_flows": len(flows),
                "is_burst": True,
                "waves_count": waves_count,
                "wave_distribution": wave_flows_distribution,
                "wave_start_times": wave_start_times,
                "target_pods": path_manager.target_pods,
                "generation_success_rate": len(flows) / num_flows if num_flows > 0 else 0
            },
            "next_flow_counter": flow_counter
        }

    def _calculate_wave_distribution(self, total_flows, waves_count):
        """计算每个波次的流量分配"""
        weights = [self.config['intensity_decay'] ** i for i in range(waves_count)]
        total_weight = sum(weights)
        
        wave_flows = []
        allocated_flows = 0
        
        for i in range(waves_count - 1):
            flows_for_wave = int(total_flows * weights[i] / total_weight)
            wave_flows.append(flows_for_wave)
            allocated_flows += flows_for_wave
        
        # 最后一波分配剩余的流量
        wave_flows.append(total_flows - allocated_flows)
        return wave_flows

    def _calculate_wave_start_times(self, waves_count):
        """计算每个波次的开始时间"""
        start_times = [0.0]  # 第一波从时间0开始
        
        current_time = 0.0
        for i in range(1, waves_count):
            interval = random.uniform(*self.config['wave_interval_range'])
            current_time += interval
            start_times.append(current_time)
        
        return start_times

    def _get_flow_size_by_ratio(self, flow_index, total_flows):
        """根据比例和索引确定流大小"""
        large_ratio = self.config['large_flow_ratio']
        medium_ratio = self.config['medium_flow_ratio']
        
        if flow_index < total_flows * large_ratio:
            return random.uniform(300, 500)  # 大流
        elif flow_index < total_flows * (large_ratio + medium_ratio):
            return random.uniform(100, 300)  # 中等流
        else:
            return random.uniform(50, 100)   # 小流

    def _create_burst_flow(self, flow_id, source, target, path, flow_size, start_time,
                          is_burst, wave_index):
        """创建爆发模式的流数据结构"""
        workload = random.choice(DL_WORKLOADS)
        
        return TrafficPattern.create_flow_dict(
            flow_id, source, target, path, flow_size, start_time,
            workload, False, is_burst, wave_index=wave_index
        )


class TimeClusterManager:
    """时间聚集管理器 - 处理流的时间点聚集"""
    
    def __init__(self, config):
        self.config = config
        self.clustering_enabled = config.get('enabled', True)
        self.clustering_ratio = config.get('clustering_ratio', 0.3)
        self.flows_per_cluster_range = config.get('flows_per_cluster_range', (2, 3))
        self.min_cluster_interval = config.get('min_cluster_interval', 0.1)
        self.path_diversity_check = config.get('path_diversity_check', True)
    
    def apply_time_clustering(self, flows, path_manager):
        """对流应用时间聚集"""
        if not self.clustering_enabled or len(flows) < 4:
            return flows
        
        print(f"  -> 应用时间聚集 (聚集比例: {self.clustering_ratio:.1%})")
        
        # 1. 选择聚集点
        cluster_timepoints = self._select_cluster_timepoints(flows)
        if not cluster_timepoints:
            return flows
        
        # 2. 将流分配到聚集点
        clustered_flows = self._cluster_flows_by_time(flows, cluster_timepoints)
        
        # 3. 确保路径多样性
        if self.path_diversity_check:
            clustered_flows = self._ensure_path_diversity(clustered_flows, path_manager)
        
        # 4. 统计聚集效果
        self._log_clustering_stats(clustered_flows, cluster_timepoints)
        
        return clustered_flows
    
    def _select_cluster_timepoints(self, flows):
        """选择聚集时间点"""
        # 获取所有流的开始时间
        start_times = [flow['start_time'] for flow in flows]
        start_times.sort()
        
        # 计算需要的聚集点数量
        target_clustered_flows = int(len(flows) * self.clustering_ratio)
        avg_flows_per_cluster = sum(self.flows_per_cluster_range) / 2
        num_clusters = max(1, int(target_clustered_flows / avg_flows_per_cluster))
        
        # 选择聚集时间点，确保间隔足够
        cluster_points = []
        time_range = max(start_times) - min(start_times)
        
        if time_range < self.min_cluster_interval * num_clusters:
            # 时间范围太小，只选择一个聚集点
            cluster_points = [random.choice(start_times)]
        else:
            # 从时间范围内选择分散的聚集点
            candidates = [t for t in start_times if 
                         all(abs(t - cp) >= self.min_cluster_interval for cp in cluster_points)]
            
            while len(cluster_points) < num_clusters and candidates:
                new_point = random.choice(candidates)
                cluster_points.append(new_point)
                # 移除太近的候选点
                candidates = [t for t in candidates if abs(t - new_point) >= self.min_cluster_interval]
        
        return cluster_points
    
    def _cluster_flows_by_time(self, flows, cluster_timepoints):
        """将流分配到聚集点"""
        flows_copy = flows.copy()
        
        for cluster_time in cluster_timepoints:
            # 找到距离聚集点最近的流
            nearby_flows = []
            for flow in flows_copy:
                time_diff = abs(flow['start_time'] - cluster_time)
                nearby_flows.append((time_diff, flow))
            
            # 按距离排序
            nearby_flows.sort(key=lambda x: x[0])
            
            # 选择要聚集的流数量
            flows_to_cluster = random.randint(*self.flows_per_cluster_range)
            flows_to_cluster = min(flows_to_cluster, len(nearby_flows))
            
            # 将选中的流时间对齐到聚集点
            for i in range(flows_to_cluster):
                flow = nearby_flows[i][1]
                flow['start_time'] = cluster_time
                flow['is_clustered'] = True
                flow['cluster_time'] = cluster_time
        
        return flows_copy
    
    def _ensure_path_diversity(self, flows, path_manager):
        """确保同一时间点的流有不同路径，并统一工作负载特征"""
        # 按时间点分组
        time_groups = defaultdict(list)
        for flow in flows:
            if flow.get('is_clustered', False):
                time_groups[flow['cluster_time']].append(flow)
        
        # 检查每个时间点的路径多样性和工作负载统一性
        for cluster_time, grouped_flows in time_groups.items():
            if len(grouped_flows) <= 1:
                continue
            
            # 1. 统一工作负载特征
            self._unify_workload_features(grouped_flows)
            
            # 2. 检查路径冲突
            paths = [tuple(flow.get('path', [])) for flow in grouped_flows]
            sources = [flow.get('source') for flow in grouped_flows]
            targets = [flow.get('target') for flow in grouped_flows]
            
            # 如果有重复的源-目标对，重新生成路径
            source_target_pairs = [(s, t) for s, t in zip(sources, targets)]
            if len(set(source_target_pairs)) != len(source_target_pairs):
                print(f"    检测到聚集点 {cluster_time:.2f}s 的路径冲突，重新生成路径")
                self._regenerate_diverse_paths(grouped_flows, path_manager)
        
        return flows
    
    def _unify_workload_features(self, flows):
        """统一同一聚集点的流的工作负载特征"""
        if len(flows) <= 1:
            return
        
        # 使用第一个流的特征作为基准
        reference_flow = flows[0]
        reference_workload = reference_flow.get('workload', {})
        reference_flow_features = reference_flow.get('flow_features', [])
        
        print(f"    统一聚集点工作负载: {reference_workload.get('model', 'Unknown')} - {reference_workload.get('dataset', 'Unknown')}")
        print(f"    统一流特征: {reference_flow_features}")
        
        # 将所有其他流的特征统一为参考流的特征
        for flow in flows[1:]:
            flow['workload'] = reference_workload.copy()
            flow['flow_features'] = reference_flow_features.copy()
    
    def _regenerate_diverse_paths(self, flows, path_manager):
        """为冲突的流重新生成多样化路径"""
        used_pairs = set()
        
        for flow in flows:
            attempts = 0
            max_attempts = 10
            
            while attempts < max_attempts:
                # 重新生成源-目标对
                if flow.get('is_congested', False):
                    source, target = path_manager.generate_congestion_flow_pair()
                else:
                    source, target = path_manager.generate_normal_flow_pair()
                
                pair = (source, target)
                if pair not in used_pairs and source and target:
                    # 获取新路径
                    new_path = path_manager.get_cross_pod_path(source, target)
                    if new_path and len(new_path) == 5:
                        flow['source'] = source
                        flow['target'] = target
                        flow['path'] = new_path
                        used_pairs.add(pair)
                        break
                
                attempts += 1
            
            if attempts >= max_attempts:
                print(f"    警告: 无法为流 {flow.get('flow_id', 'unknown')} 生成唯一路径")
    
    def _log_clustering_stats(self, flows, cluster_timepoints):
        """记录聚集统计信息"""
        clustered_flows = [f for f in flows if f.get('is_clustered', False)]
        
        print(f"    聚集统计: {len(clustered_flows)} 个流聚集到 {len(cluster_timepoints)} 个时间点")
        
        # 按聚集点统计
        cluster_stats = defaultdict(int)
        for flow in clustered_flows:
            cluster_stats[flow['cluster_time']] += 1
        
        for cluster_time, count in cluster_stats.items():
            print(f"      时间点 {cluster_time:.2f}s: {count} 个流")


class DataGenConfig:
    """数据生成配置类 - 异步流模式 + 时间聚集"""
    
    def __init__(self):
        # === 异步流数据生成参数 ===
        self.params = {
            # 批次数量配置
            "num_batches": 100,                      # 训练批次数量
            "num_validation_batches": 20,           # 验证批次数量
            "num_test_batches": 1,                 # 测试批次数量
            
            # 全局配置
            "output_base_dir": "datasets",           # 最终输出基础目录名 (用于IID合规)
            "ensure_iid": True,                      # 确保IID合规性
            
            # === 时间聚集配置 ===
            "time_clustering": {
                "enabled": True,                     # 启用时间聚集
                "clustering_ratio": 0.7,             # 30%的流参与聚集
                "flows_per_cluster_range": (2, 3),   # 每个聚集点2-3条流
                "min_cluster_interval": 0.1,         # 聚集点最小间隔0.1秒
                "path_diversity_check": True         # 启用路径多样性检查
            },
            
            # --- 流量模式配置 (策略模式核心) ---
            "traffic_patterns": {
                "burst_ratio": 0.3,                 # 30%的批次为爆发模式
                "patterns": {
                    "normal": {
                        "flows_per_batch_range": (30, 35),
                        "flow_arrival_interval_range": (0.0, 2.0),
                        "flow_size_range": (50, 500),
                        "congestion_ratio_range": (0.8, 1),
                        "target_pods_count": 10,
                    },
                    "burst": {
                        "flow_count_range": (5, 10),
                        "time_window": (0.0, 0.5),
                        "waves_count_range": (1, 2),
                        "wave_interval_range": (1, 2),
                        "intensity_decay": 0.7,
                        "target_pods_count": 10,
                        "congestion_flow_ratio": 0.3,
                        "large_flow_ratio": 0.4,
                        "medium_flow_ratio": 0.4,
                        "small_flow_ratio": 0.2,
                    }
                }
            }
        }
    
    def update_params(self, **kwargs):
        """更新参数"""
        self.params.update(kwargs)
    
    def print_config(self):
        """打印当前配置"""
        print(f"\n=== 数据生成配置 ===")
        for key, value in self.params.items():
            print(f"{key}: {value}")
        print("=" * 40)

# 全局配置实例
config = DataGenConfig()

# 定义深度学习工作负载
DL_WORKLOADS = [
    {
        "model": "ResNet-50",
        "dataset": "ImageNet",
        "params": 25.6e6,
        "compute_intensity": "中",
        "comm_intensity": "中"
    },
    {
        "model": "BERT-Large",
        "dataset": "SQuAD",
        "params": 340e6,
        "compute_intensity": "高",
        "comm_intensity": "高"
    },
    {
        "model": "DLRM",
        "dataset": "Criteo",
        "params": 130e6,
        "compute_intensity": "中",
        "comm_intensity": "中高"
    },
    {
        "model": "VGG-16",
        "dataset": "ImageNet",
        "params": 138e6,
        "compute_intensity": "中",
        "comm_intensity": "高"
    },
    {
        "model": "DeepSpeech2",
        "dataset": "LibriSpeech",
        "params": 120e6,
        "compute_intensity": "中",
        "comm_intensity": "中"
    }
]





class PathManager:
    """管理路径选择 - 简化版，基于Pod结构"""

    def __init__(self, clos):
        self.clos = clos
        self.target_pods = []  # 目标Pod列表，用于制造拥塞
        self.pod_hosts = self._group_hosts_by_pod()
        
    def _group_hosts_by_pod(self):
        """将主机按Pod分组"""
        pod_hosts = {}
        hosts = self.clos.nodes['H']
        for i, host in enumerate(hosts):
            pod_id = i // 4  # 每个Pod有4个主机
            if pod_id not in pod_hosts:
                pod_hosts[pod_id] = []
            pod_hosts[pod_id].append(host)
        
        print(f"网络包含 {len(pod_hosts)} 个Pod，每个Pod有4个主机")
        return pod_hosts
    
    def select_target_pods(self, num_target_pods=2):
        """选择目标Pod用于制造拥塞"""
        available_pods = list(self.pod_hosts.keys())
        self.target_pods = random.sample(available_pods, min(num_target_pods, len(available_pods)))
        
        print(f"已选择目标Pod: {self.target_pods}")
        for pod_id in self.target_pods:
            print(f"  Pod {pod_id}: {self.pod_hosts[pod_id]}")
        
        return self.target_pods
    
    def get_cross_pod_path(self, source, target):
        """生成跨Pod的4跳路径"""
        try:
            # 在Clos网络中，跨Pod通信自然就是4跳
            all_paths = list(self.clos.find_all_paths(source, target, cutoff=5))
            four_hop_paths = [p for p in all_paths if len(p) == 5]  # 4跳=5个节点
            
            if four_hop_paths:
                return random.choice(four_hop_paths)
            else:
                return None
        except:
            return None
    
    def get_intra_pod_path(self, source, target):
        """生成Pod内的2跳路径"""
        try:
            all_paths = list(self.clos.find_all_paths(source, target, cutoff=3))
            two_hop_paths = [p for p in all_paths if len(p) == 3]  # 2跳=3个节点
            
            if two_hop_paths:
                return random.choice(two_hop_paths)
            else:
                return None
        except:
            return None
    
    def generate_congestion_flow_pair(self):
        """生成指向目标Pod的拥塞流源目标对"""
        if not self.target_pods:
            return None, None
            
        # 选择目标Pod
        target_pod = random.choice(self.target_pods)
        target_host = random.choice(self.pod_hosts[target_pod])
        
        # 选择源主机（来自其他Pod）
        source_pod_candidates = [pod_id for pod_id in self.pod_hosts.keys() if pod_id != target_pod]
        if not source_pod_candidates:
            return None, None
            
        source_pod = random.choice(source_pod_candidates)
        source_host = random.choice(self.pod_hosts[source_pod])
        
        return source_host, target_host
    
    def generate_normal_flow_pair(self):
        """生成正常流的源目标对（避开目标Pod）"""
        non_target_pods = [pod_id for pod_id in self.pod_hosts.keys() if pod_id not in self.target_pods]
        
        if len(non_target_pods) < 2:
            # 如果非目标Pod太少，随机选择任意两个主机
            all_hosts = []
            for hosts in self.pod_hosts.values():
                all_hosts.extend(hosts)
            
            if len(all_hosts) < 2:
                return None, None
            
            selected = random.sample(all_hosts, 2)
            return selected[0], selected[1]
        
        # 从非目标Pod中选择源和目标
        selected_pods = random.sample(non_target_pods, 2)
        source_host = random.choice(self.pod_hosts[selected_pods[0]])
        target_host = random.choice(self.pod_hosts[selected_pods[1]])
        
        return source_host, target_host


class TimeBasedFlowScheduler:
    """基于时间的流调度器 - 流水线模型版本
    采用流水线(Pipelining)/直通交换(Cut-through)模型进行仿真。
    一个流的速率由其路径上的瓶颈链路决定。
    """

    def __init__(self, topology, tasks_data=None, timestep=0.01, max_time=10.0):
        self.topology = topology
        self.timestep = timestep
        self.max_time = max_time
        self.current_time = 0.0
        
        # 任务和流状态管理
        self.tasks_data = tasks_data if tasks_data else []
        self.pending_flows = {}   # 等待开始的流 {start_time: [flow_info, ...]}
        self.active_flows = {}    # 正在传输的流 {flow_id: flow_info}
        self.completed_flows = {} # 已完成的流 {flow_id: flow_info}

        # 性能优化：增量维护边竞争计数
        self.edge_contention = defaultdict(int)

        # 仿真结果
        self.flow_completion_times = {} # 记录每个流的完成时间 {flow_id: completion_time}

        # 初始化任务
        if self.tasks_data:
            self.initialize_flows()

    def initialize_flows(self):
        """
        初始化所有流，将它们放入等待队列。
        """
        for task in self.tasks_data:
            flow_id = task['task_id']
            start_time = round(task.get('comm_start_time', 0.0), 4) # 对开始时间进行取整以分组
            
            flow_info = {
                'flow_id': flow_id,
                'path': task['paths'][0],
                'size': task['gradient_size_mb'],
                'start_time': start_time,
                'total_bytes_sent': 0.0,
                'end_time': -1.0
            }

            if start_time not in self.pending_flows:
                self.pending_flows[start_time] = []
            
            # 将流信息添加到对应开始时间的列表中
            self.pending_flows[start_time].append(flow_info)
        
        # 打印初始化信息
        total_flows = len(self.tasks_data)
        print(f"调度器初始化完成: {total_flows} 个流被加载到等待队列。")

    def _add_flow_to_contention(self, flow_info):
        """当一个流开始时，增加其路径上所有边的计数值"""
        path = flow_info['path']
        for i in range(len(path) - 1):
            edge = (path[i], path[i+1])
            self.edge_contention[edge] += 1

    def _remove_flow_from_contention(self, flow_info):
        """当一个流结束时，减少其路径上所有边的计数值"""
        path = flow_info['path']
        for i in range(len(path) - 1):
            edge = (path[i], path[i+1])
            self.edge_contention[edge] -= 1
            if self.edge_contention[edge] == 0:
                del self.edge_contention[edge] # 保持字典干净

    def simulate_time_based(self):
        """
        运行基于时间的流水线模型仿真。
        """
        print(f"开始流水线模型仿真，最大时间: {self.max_time}s，时间步长: {self.timestep}s")
        
        # 按时间排序等待队列的键
        pending_times = sorted(self.pending_flows.keys())
        
        from tqdm import tqdm
        pbar = tqdm(total=len(self.tasks_data), desc="Simulating Flows")

        while self.current_time < self.max_time:
            
            # 1. 启动新到达的流
            # 检查是否有在当前时间步或之前需要开始的流
            while pending_times and self.current_time >= pending_times[0]:
                start_time = pending_times.pop(0)
                for flow_info in self.pending_flows[start_time]:
                    self.active_flows[flow_info['flow_id']] = flow_info
                    self._add_flow_to_contention(flow_info)  # 增量更新
                    # print(f"时间 {self.current_time:.3f}s: 流 {flow_info['flow_id']} 开始传输。")

            # 如果没有活动流，直接跳到下一个有流开始的时间点，以加速仿真
            if not self.active_flows:
                if not pending_times:
                    # 所有流都完成了
                    print("所有流均已完成或启动，仿真结束。")
                    break
                else:
                    self.current_time = pending_times[0]
                    continue # 跳到下一个循环以启动新流

            # 2. 计算每个活跃流的瓶颈带宽
            flow_rates = {}
            for flow_id, flow_info in self.active_flows.items():
                min_bw = float('inf')
                path = flow_info['path']
                for i in range(len(path) - 1):
                    edge = (path[i], path[i+1])
                    try:
                        capacity = self.topology.G[edge[0]][edge[1]]['btw']
                    except KeyError:
                        capacity = 16000 # 默认带宽

                    # 从预维护的计数中获取竞争数 - O(1)操作
                    contention = self.edge_contention[edge]
                    
                    # 公平分享带宽
                    shared_bw = capacity / contention if contention > 0 else capacity
                    if shared_bw < min_bw:
                        min_bw = shared_bw
                
                flow_rates[flow_id] = min_bw

            # 3. 根据瓶颈速率更新流的传输进度
            flows_to_complete = []
            for flow_id, flow_info in self.active_flows.items():
                rate = flow_rates[flow_id]
                
                # 在这个时间步内传输的数据量
                bytes_sent = rate * self.timestep
                flow_info['total_bytes_sent'] += bytes_sent
                
                # 检查流是否完成
                if flow_info['total_bytes_sent'] >= flow_info['size']:
                    flows_to_complete.append(flow_id)

            # 4. 处理已完成的流
            if flows_to_complete:
                for flow_id in flows_to_complete:
                    if flow_id in self.active_flows: # 确保流还在活跃列表中
                        completed_flow = self.active_flows.pop(flow_id)
                        
                        # 计算精确的完成时间
                        overshoot_bytes = completed_flow['total_bytes_sent'] - completed_flow['size']
                        overshoot_time = overshoot_bytes / flow_rates[flow_id] if flow_rates[flow_id] > 0 else 0
                        completion_time = self.current_time + self.timestep - overshoot_time
                        
                        completed_flow['end_time'] = completion_time
                        self.completed_flows[flow_id] = completed_flow
                        self.flow_completion_times[flow_id] = completion_time
                        
                        # 增量更新竞争计数
                        self._remove_flow_from_contention(completed_flow)
                        
                        # print(f"时间 {completion_time:.3f}s: 流 {flow_id} 完成传输。")
                        pbar.update(1)

            # 5. 推进时间
            self.current_time += self.timestep

            # 检查是否所有流都已完成
            if not self.active_flows and not pending_times:
                print(f"所有流已完成，仿真在时间 {self.current_time:.3f}s 结束。")
                break
        
        pbar.close()
        self.calculate_statistics()
        return self.get_simulation_results()

    def get_simulation_results(self):
        """
        返回仿真结果，主要为每个流的完成时间(FCT)和总延迟。
        """
        results = {}
        for flow_id, flow_info in self.completed_flows.items():
            fct = flow_info['end_time'] - flow_info['start_time']
            results[flow_id] = {
                "fct": fct,
                "start_time": flow_info['start_time'],
                "end_time": flow_info['end_time'],
                "size_mb": flow_info['size']
            }
        
        # 处理未完成的流 (如果仿真时间耗尽)
        for flow_id, flow_info in self.active_flows.items():
             results[flow_id] = {
                "fct": -1, # -1 表示未完成
                "start_time": flow_info['start_time'],
                "end_time": -1,
                "size_mb": flow_info['size']
            }
        return results

    def calculate_statistics(self):
        """计算最终的统计信息"""
        total_flows = len(self.tasks_data)
        completed_count = len(self.completed_flows)
        
        print(f"\n=== 仿真统计 (最终时间: {self.current_time:.3f}s) ===")
        print(f"总流数: {total_flows}")
        print(f"已完成: {completed_count}")
        print(f"未完成: {total_flows - completed_count}")
        
        if completed_count > 0:
            completion_rate = completed_count / total_flows
            fcts = [res['fct'] for res in self.get_simulation_results().values() if res['fct'] != -1]
            if fcts:
                avg_fct = np.mean(fcts)
                print(f"完成率: {completion_rate:.2%}")
                print(f"平均流完成时间 (FCT): {avg_fct:.4f}s")
        else:
            print("没有流完成。")





class FlowDataGenerator:
    """流数据生成器 (策略执行者) + 时间聚集支持"""
    
    def __init__(self, clos_topology, traffic_patterns_config):
        self.clos = clos_topology
        self.path_manager = PathManager(self.clos)
        self.config = traffic_patterns_config

        # 创建所有可用的策略实例
        self.strategies = {
            "normal": NormalTraffic(self.config['patterns']['normal']),
            "burst": BurstTraffic(self.config['patterns']['burst'])
        }
        
        # 创建时间聚集管理器
        self.cluster_manager = TimeClusterManager(self.config.get('time_clustering', {}))

    def generate_flow_batch(self, batch_id, flow_counter_start=0):
        """根据配置选择一个策略来生成批次，并应用时间聚集"""
        
        # 决定使用哪个策略
        if random.random() < self.config['burst_ratio']:
            strategy = self.strategies['burst']
            print("  -> 选择 'BurstTraffic' 策略")
        else:
            strategy = self.strategies['normal']
            print("  -> 选择 'NormalTraffic' 策略")
            
        # 执行策略生成流
        batch_result = strategy.generate_flows(batch_id, self.path_manager, flow_counter_start)
        
        # 应用时间聚集
        if batch_result and batch_result.get("flows"):
            print("  -> 应用时间聚集处理")
            clustered_flows = self.cluster_manager.apply_time_clustering(
                batch_result["flows"], self.path_manager
            )
            batch_result["flows"] = clustered_flows
            
            # 更新元数据中的聚集信息
            clustered_count = len([f for f in clustered_flows if f.get('is_clustered', False)])
            batch_result["metadata"]["clustered_flows"] = clustered_count
            batch_result["metadata"]["clustering_ratio"] = clustered_count / len(clustered_flows) if clustered_flows else 0
        
        return batch_result


def generate_raw_batches(generation_plan, scenario_definitions):
    """
    根据生成计划生成原始批次数据。
    
    Args:
        generation_plan (dict): 包含要生成的批次数和输出目录的字典。
        scenario_definitions (dict): 定义了'easy', 'medium', 'hard'等场景的参数。
        
    Returns:
        dict: 一个字典，键是场景名称，值是该场景下生成的批次文件路径列表。
    """
    raw_data_dir = generation_plan['raw_data_output_dir']
    os.makedirs(raw_data_dir, exist_ok=True)
    
    batches_to_generate = generation_plan['batches_to_generate']
    
    print("--- 阶段1: 开始生成原始数据池 ---")
    print(f"原始数据将保存在: {raw_data_dir}")
    print(f"生成计划: {batches_to_generate}")
    
    generated_files_by_scenario = defaultdict(list)
    global_flow_counter = 0

    # 为每种场景生成指定数量的批次
    for scenario_name, num_batches in batches_to_generate.items():
        if scenario_name not in scenario_definitions:
            print(f"警告: 在场景定义中找不到 '{scenario_name}'，跳过。")
            continue

        print(f"\n--- 正在生成 '{scenario_name}' 场景的 {num_batches} 个批次 ---")
        scenario_config = scenario_definitions[scenario_name]
        
        # 为该场景创建目录
        scenario_dir = os.path.join(raw_data_dir, scenario_name)
        os.makedirs(scenario_dir, exist_ok=True)

        for i in tqdm(range(num_batches), desc=f"生成 {scenario_name} 批次"):
            batch_id = f"{scenario_name}_batch{i}"
            
            clos = Clos(4, 4, 4, ps_bandwidth=16000, sh_bandwidth=16000)
            clos.build()
            
            # 使用从场景定义中获取的配置
            generator = FlowDataGenerator(clos, scenario_config)
            
            batch = generator.generate_flow_batch(batch_id, global_flow_counter)
            global_flow_counter = batch.get("next_flow_counter", global_flow_counter)
            
            if not batch["flows"]:
                print(f"警告: 批次 {batch_id} 没有生成任何流，跳过。")
                continue
            
            batch_data = _simulate_single_batch(clos, batch, batch_id, "raw")
            
            if batch_data:
                # 添加场景元数据
                batch_data['metadata']['scenario'] = scenario_name
                
                filename = f"batch_{i}.json"
                filepath = os.path.join(scenario_dir, filename)
                
                with open(filepath, 'w') as f:
                    json.dump(batch_data, f, indent=4)
                
                generated_files_by_scenario[scenario_name].append(filepath)

    total_generated = sum(len(files) for files in generated_files_by_scenario.values())
    print(f"\n--- 原始数据池生成完成 ---")
    print(f"总共成功生成 {total_generated} 个批次。")
    for scenario, files in generated_files_by_scenario.items():
        print(f"  - {scenario}: {len(files)} 个批次")
        
    return generated_files_by_scenario


def stratified_split_batches(generated_files_by_scenario, partitioning_plan):
    """
    对生成的批次进行分层采样和划分。

    Args:
        generated_files_by_scenario (dict): 按场景分组的已生成文件路径字典。
        partitioning_plan (dict): 包含划分大小和采样分布的字典。

    Returns:
        dict: 包含 'train', 'validation', 'test' 划分的文件路径字典。
    """
    print("\n--- 阶段2: 开始分层采样与划分 ---")
    
    target_splits = partitioning_plan['splits']
    distribution = partitioning_plan['sampling_distribution']
    
    total_requested_batches = sum(target_splits.values())
    total_available_batches = sum(len(v) for v in generated_files_by_scenario.values())

    if total_requested_batches > total_available_batches:
        raise ValueError(
            f"请求的批次总数 ({total_requested_batches}) 超过了可用的批次总数 ({total_available_batches})。"
            "请减少数据集大小或生成更多的原始数据。"
        )
    
    print(f"目标划分: {target_splits}")
    print(f"目标分布: {distribution}")

    # --- 1. 计算每个数据集和场景的理想需求 ---
    ideal_needs = defaultdict(dict)
    for split_name, total_size in target_splits.items():
        for scenario, ratio in distribution.items():
            ideal_needs[split_name][scenario] = total_size * ratio
    
    # --- 2. 创建一个全局分配计划，避免饥饿 ---
    allocation_plan = defaultdict(dict)
    available_counts = {s: len(f) for s, f in generated_files_by_scenario.items()}

    for scenario in distribution.keys():
        # 计算该场景的总需求
        total_scenario_demand = sum(ideal_needs[s][scenario] for s in target_splits)
        
        if total_scenario_demand > available_counts.get(scenario, 0):
            print(f"警告: 场景 '{scenario}' 的需求 ({total_scenario_demand:.2f}) 超过可用数量 ({available_counts.get(scenario, 0)})。")
            print("         将按比例缩减该场景的分配。")
            
            # 按比例缩减
            scaling_factor = available_counts.get(scenario, 0) / total_scenario_demand if total_scenario_demand > 0 else 0
            for split_name in target_splits:
                ideal_needs[split_name][scenario] *= scaling_factor

    # --- 3. 分配整数部分 ---
    allocated_counts = defaultdict(lambda: defaultdict(int))
    fractional_remainders = defaultdict(lambda: defaultdict(float))
    
    for split_name in target_splits:
        for scenario in distribution:
            ideal_val = ideal_needs[split_name].get(scenario, 0)
            allocated_counts[split_name][scenario] = int(ideal_val)
            fractional_remainders[split_name][scenario] = ideal_val - int(ideal_val)

    # --- 4. 分配小数部分 ---
    # 汇总每个场景已分配的数量
    total_allocated_per_scenario = defaultdict(int)
    for scenario in distribution:
        total_allocated_per_scenario[scenario] = sum(allocated_counts[s][scenario] for s in target_splits)

    # 创建一个扁平的、可排序的剩余项列表
    remainder_list = []
    for split_name in target_splits:
        for scenario in distribution:
            remainder_list.append((fractional_remainders[split_name][scenario], split_name, scenario))
    
    # 按从大到小的顺序排序
    remainder_list.sort(key=lambda x: x[0], reverse=True)

    # 逐一分配剩余部分，直到满足总需求或资源耗尽
    for _, split_name, scenario in remainder_list:
        # 检查该数据集是否还需要批次
        current_split_total = sum(allocated_counts[split_name].values())
        if current_split_total >= target_splits[split_name]:
            continue # 该数据集已满

        # 检查该场景是否还有可用批次
        if total_allocated_per_scenario[scenario] < available_counts.get(scenario, 0):
            allocated_counts[split_name][scenario] += 1
            total_allocated_per_scenario[scenario] += 1

    # --- 5. 最终修正，确保数据集大小完全正确 ---
    for split_name, target_size in target_splits.items():
        current_size = sum(allocated_counts[split_name].values())
        discrepancy = target_size - current_size
        
        # 如果需要更多批次
        if discrepancy > 0:
            # 按小数部分大小，从最需要的场景开始添加
            for _, s_name, scenario in remainder_list:
                if s_name == split_name and discrepancy > 0:
                    if total_allocated_per_scenario[scenario] < available_counts.get(scenario, 0):
                        allocated_counts[split_name][scenario] += 1
                        total_allocated_per_scenario[scenario] += 1
                        discrepancy -= 1
        
        # 如果需要减少批次
        elif discrepancy < 0:
            # 按小数部分大小，从最不需要的场景开始移除
            for _, s_name, scenario in reversed(remainder_list):
                if s_name == split_name and discrepancy < 0:
                    if allocated_counts[split_name][scenario] > 0:
                        allocated_counts[split_name][scenario] -= 1
                        total_allocated_per_scenario[scenario] -= 1
                        discrepancy += 1
    
    allocation_plan = allocated_counts
    print("\n最终分配计划:")
    for split, scenarios in allocation_plan.items():
        print(f"  - {split}: {dict(scenarios)}")


    # --- 6. 根据分配计划选择文件 ---
    # 复制并打乱可用文件列表以确保随机性
    available_files = {s: random.sample(f, len(f)) for s, f in generated_files_by_scenario.items()}
    
    final_splits = defaultdict(list)
    for split_name, scenarios in allocation_plan.items():
        for scenario, count in scenarios.items():
            # 从可用文件列表中取出所需数量的文件
            files_to_add = available_files[scenario][:count]
            final_splits[split_name].extend(files_to_add)
            
            # 更新可用文件列表
            available_files[scenario] = available_files[scenario][count:]
            
    print("\n分层划分完成:")
    for name, files in final_splits.items():
        print(f"  - {name}: {len(files)} 个批次")
        
    return final_splits


def move_batches_to_splits(final_splits, output_base_dir):
    """将批次文件从临时目录移动到最终目录结构
    
    Args:
        final_splits (dict): 划分后的文件列表字典
        output_base_dir (str): 最终输出基础目录
        
    Returns:
        dict: 移动后的文件统计
    """
    import shutil
    print(f"\n--- 阶段3: 移动文件到最终目录 ---")
    
    # 创建最终目录结构
    for split_name in final_splits.keys():
        split_path = os.path.join(output_base_dir, split_name)
        # 如果目录已存在，先清空
        if os.path.exists(split_path):
            shutil.rmtree(split_path)
        os.makedirs(split_path, exist_ok=True)
    
    moved_stats = {}
    
    for split_name, file_list in final_splits.items():
        moved_count = 0
        print(f"正在处理 '{split_name}' 数据集...")
        
        # 再次打乱，确保最终文件编号是随机的
        random.shuffle(file_list)
        
        for new_idx, src_path in enumerate(tqdm(file_list, desc=f"移动 {split_name} 文件")):
            # 目标文件路径 - 重新编号
            dst_path = os.path.join(output_base_dir, split_name, f"batch_{new_idx}.json")
            
            if os.path.exists(src_path):
                shutil.copy(src_path, dst_path) # 使用copy而不是move，以保留原始数据
                moved_count += 1
            else:
                print(f"警告: 源文件不存在: {src_path}")
        
        moved_stats[split_name] = moved_count
        print(f"移动 {split_name} 数据集: {moved_count} 个批次")
    
    return moved_stats


def validate_iid_compliance(output_base_dir, final_splits, partitioning_plan):
    """验证IID合规性 - 检查三个数据集的分布一致性
    
    Args:
        output_base_dir (str): 输出基础目录
        final_splits (dict): 划分后的文件列表字典
        partitioning_plan (dict): 包含目标分布的字典
        
    Returns:
        dict: 验证结果统计
    """
    validation_results = {
        "splits_stats": {},
        "scenario_ratios": {},
        "iid_compliance": True,
        "distribution_variance": 0.0
    }
    
    print("\n--- 阶段4: 验证IID合规性 ---")
    
    all_scenario_ratios = []

    for split_name, file_list in final_splits.items():
        split_dir = os.path.join(output_base_dir, split_name)
        
        scenario_counts = defaultdict(int)
        total_batches = 0
        total_flows = 0
        
        for batch_idx in range(len(file_list)):
            batch_file = os.path.join(split_dir, f"batch_{batch_idx}.json")
            
            if os.path.exists(batch_file):
                with open(batch_file, 'r') as f:
                    batch_data = json.load(f)
                
                total_batches += 1
                total_flows += len(batch_data.get("flows", []))
                
                scenario = batch_data.get("metadata", {}).get("scenario", "unknown")
                scenario_counts[scenario] += 1
        
        # 计算该数据集内各场景的实际比例
        actual_ratios = {s: c / total_batches if total_batches > 0 else 0 for s, c in scenario_counts.items()}
        all_scenario_ratios.append(actual_ratios)

        validation_results["splits_stats"][split_name] = {
            "total_batches": total_batches,
            "total_flows": total_flows,
            "scenario_counts": dict(scenario_counts),
            "actual_ratios": actual_ratios
        }
        
        validation_results["scenario_ratios"][split_name] = actual_ratios
        
        print(f"\n{split_name.upper()} 数据集:")
        print(f"  总批次数: {total_batches}")
        print(f"  总流数: {total_flows}")
        print(f"  场景分布 (计数): {dict(scenario_counts)}")
        print(f"  场景分布 (比例):")
        for s, r in actual_ratios.items():
            target_r = partitioning_plan['sampling_distribution'].get(s, 0)
            print(f"    - {s}: {r:.3f} (目标: {target_r:.3f})")
    
    # 计算所有场景类型的分布方差
    target_distribution = partitioning_plan['sampling_distribution']
    variances = []
    for scenario in target_distribution.keys():
        ratios_for_scenario = [split_ratios.get(scenario, 0) for split_ratios in all_scenario_ratios]
        if len(ratios_for_scenario) > 1:
            mean_ratio = np.mean(ratios_for_scenario)
            variance = np.var(ratios_for_scenario)
            variances.append(variance)
            print(f"\n场景 '{scenario}' 的分布一致性:")
            print(f"  各数据集比例: {[f'{r:.3f}' for r in ratios_for_scenario]}")
            print(f"  平均比例: {mean_ratio:.3f}")
            print(f"  方差: {variance:.6f}")

    if variances:
        total_variance = np.mean(variances)
        validation_results["distribution_variance"] = total_variance
        
        print(f"\n总体分布一致性:")
        print(f"  平均方差: {total_variance:.6f}")
        
        # 判断IID合规性 - 方差应该很小
        if total_variance > 0.01:  # 1%的方差阈值
            validation_results["iid_compliance"] = False
            print(f"  ⚠️  IID合规性检查失败: 方差过大 ({total_variance:.6f} > 0.01)")
        else:
            print(f"  ✅ IID合规性检查通过: 分布一致")
    
    return validation_results


def _simulate_single_batch(clos, batch, batch_idx, dataset_name):
    """仿真单个批次并获取延迟数据 - 适配流水线调度器"""
    try:
        # 准备所有流的任务数据
        all_tasks = []
        
        for flow in batch["flows"]:
            task = {
                "task_id": flow["flow_id"],
                "source": flow["source"],
                "target": flow["target"],
                "gradient_size_mb": flow["flow_features"][0],
                "comm_start_time": flow["start_time"],
                "paths": [flow["path"]]
            }
            all_tasks.append(task)
        
        if not all_tasks:
            return None

        # 创建新的流水线调度器
        scheduler = TimeBasedFlowScheduler(
            clos,
            tasks_data=all_tasks,
            timestep=0.001,  # 使用更小的时间步以提高精度
            max_time=500.0  # 增加额外时间确保所有流完成
        )
        
        # 运行仿真并获取结果
        simulation_results = scheduler.simulate_time_based()
        
        # 构建批次数据
        batch_data = {
            "batch_id": batch["batch_id"],
            "flows": [],
            "metadata": batch.get("metadata", {}) # 保留元数据
        }
        
        # 保存爆发模式信息到全局统计中
        if batch.get("metadata", {}).get("is_burst", False):
            batch_data["_burst_info"] = batch["metadata"]
            
        # 处理每个流的数据
        for flow in batch["flows"]:
            flow_id = flow["flow_id"]
            
            # 获取该流的仿真结果
            result = simulation_results.get(flow_id, {"fct": -1}) # 如果找不到则标记为-1
            
            # 构建流数据 - 目标变量现在是FCT，包含聚集信息，不包含is_congested
            flow_data = {
                "inputs": {
                    "flow_id": flow_id,
                    "flow_features": flow["flow_features"],
                    "path": flow["path"],
                    "start_time": flow["start_time"],
                    "model": flow["workload"]["model"],
                    "dataset": flow["workload"]["dataset"],
                    "parameters": flow["workload"]["parameters"],
                    "is_clustered": flow.get("is_clustered", False),
                    "cluster_time": flow.get("cluster_time", None)
                },
                "time_delay": result["fct"]  # 这里的time_delay现在是总的FCT
            }
            
            # 只保留成功完成的流
            if result["fct"] > 0:
                batch_data["flows"].append(flow_data)

        # 只有在批次中至少有一个成功完成的流时才返回数据
        if not batch_data["flows"]:
            print(f"警告: 批次 {batch_idx} 没有成功完成任何流，将跳过此批次。")
            return None

        return batch_data
        
    except Exception as e:
        print(f"仿真批次 {batch_idx} 时出错: {e}")
        import traceback
        traceback.print_exc()
        return None


def generate_dataset_from_blueprint(blueprint_path):
    """
    从蓝图文件生成数据集的端到端流程。
    
    流程：
    1. 加载蓝图和场景定义。
    2. 生成原始批次数据并保存到 `raw_data_output_dir`。
    3. 根据 `partitioning_plan` 对原始数据进行分层采样和划分。
    4. 将划分好的批次复制到 `final_dataset_output_dir`。
    5. 验证最终数据集的IID合规性。
    6. 生成报告。
    """
    # --- 加载配置 ---
    print(f"--- 从蓝图文件加载配置: {blueprint_path} ---")
    with open(blueprint_path, 'r') as f:
        blueprint = yaml.safe_load(f)
    
    # 假设场景定义文件与蓝图文件在同一目录或可访问
    scenario_def_path = "scenarios.yaml" 
    print(f"--- 加载场景定义: {scenario_def_path} ---")
    with open(scenario_def_path, 'r') as f:
        scenario_definitions = yaml.safe_load(f)

    generation_plan = blueprint['generation_plan']
    partitioning_plan = blueprint['partitioning_plan']
    
    print("\n--- 配置加载完成 ---")
    print("生成计划:")
    print(f"  - 原始数据输出目录: {generation_plan['raw_data_output_dir']}")
    print(f"  - 待生成批次: {generation_plan['batches_to_generate']}")
    print("划分计划:")
    print(f"  - 最终数据集目录: {partitioning_plan['final_dataset_output_dir']}")
    print(f"  - 采样分布: {partitioning_plan['sampling_distribution']}")
    print(f"  - 目标大小: {partitioning_plan['splits']}")
    
    try:
        # --- 步骤 1: 生成原始数据 ---
        generated_files = generate_raw_batches(
            generation_plan,
            scenario_definitions['scenarios']
        )
        
        # --- 步骤 2: 分层采样与划分 ---
        final_splits = stratified_split_batches(
            generated_files, 
            partitioning_plan
        )
        
        # --- 步骤 3: 移动文件到最终目录 ---
        output_base_dir = partitioning_plan['final_dataset_output_dir']
        moved_stats = move_batches_to_splits(final_splits, output_base_dir)
        
        # --- 步骤 4: 验证IID合规性 ---
        validation_results = validate_iid_compliance(output_base_dir, final_splits, partitioning_plan)
        
        # --- 步骤 5: 生成报告 ---
        print(f"\n--- 阶段5: 生成报告 ---")
        _generate_iid_compliance_report(output_base_dir, validation_results, partitioning_plan)
        
        print(f"\n--- 数据集生成流程完成 ---")
        print(f"原始数据保留在: {generation_plan['raw_data_output_dir']}")
        print(f"最终数据集已生成于: {output_base_dir}")
        
        if validation_results["iid_compliance"]:
            print("✅ IID合规性验证通过")
        else:
            print("⚠️ IID合规性验证失败")
        
        return {
            "validation_results": validation_results,
            "moved_stats": moved_stats,
            "final_splits": final_splits,
            "output_base_dir": output_base_dir
        }
        
    except Exception as e:
        print(f"数据生成过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None



def _generate_iid_compliance_report(output_base_dir, validation_results, partitioning_plan):
    """生成IID合规性报告"""
    
    doc_content = """# IID合规性数据生成报告

## 概述
此文档记录了数据集的IID (Independent and Identically Distributed) 合规性验证结果。
该数据集是根据一个声明式的蓝图文件生成的，确保了流程的可复现性。

## 数据生成流程

1.  **定义蓝图 (`campaign.yaml`)**:
    *   **生成计划**: 指定需要为每个场景（如 `easy`, `medium`, `hard`）生成多少原始数据批次。
    *   **划分计划**: 定义最终训练/验证/测试集的构成，包括每个集的大小和内部应遵循的场景分布比例。
2.  **生成原始数据池**: 根据`生成计划`，在 `data_gen/rar_data` 目录中创建所有必需的批次。
3.  **分层采样**: 使用`划分计划`中的分布比例，从原始数据池中为每个数据集（train/validation/test）计算需要多少各种场景的批次。此步骤采用了一种健壮的分配算法，以精确匹配目标数量和分布。
4.  **创建最终数据集**: 将采样出的文件复制到 `data_gen/datasets` 中的相应子目录，并重新编号。
5.  **验证与报告**: 自动分析最终数据集的分布，并生成此报告。

### 划分计划参数
"""
    
    # 添加关键配置参数
    splits = partitioning_plan['splits']
    distribution = partitioning_plan['sampling_distribution']

    doc_content += "- **目标数据集大小**:\n"
    for name, size in splits.items():
        doc_content += f"  - `{name}`: {size} 批次\n"
        
    doc_content += "- **目标场景分布**:\n"
    for name, ratio in distribution.items():
        doc_content += f"  - `{name}`: {ratio*100:.2f}%\n"

    doc_content += f"""
## 验证结果

### 整体统计
"""
    
    # 添加整体统计
    total_batches = sum(stats["total_batches"] for stats in validation_results["splits_stats"].values())
    total_flows = sum(stats["total_flows"] for stats in validation_results["splits_stats"].values())
    
    doc_content += f"- **总批次数**: {total_batches}\n"
    doc_content += f"- **总流数**: {total_flows}\n"
    doc_content += f"- **分布平均方差**: {validation_results['distribution_variance']:.6f}\n"
    
    # IID合规性状态
    compliance_status = "✅ 通过" if validation_results["iid_compliance"] else "❌ 失败"
    doc_content += f"- **IID合规性**: {compliance_status}\n\n"
    
    # 详细的数据集统计
    doc_content += "### 各数据集详细统计\n\n"
    
    for split_name, stats in validation_results["splits_stats"].items():
        doc_content += f"#### {split_name.upper()} 数据集\n"
        doc_content += f"- 总批次数: {stats['total_batches']}\n"
        doc_content += f"- 总流数: {stats['total_flows']}\n"
        doc_content += f"- **场景计数**: `{stats['scenario_counts']}`\n"
        doc_content += "- **实际场景比例**:\n"
        for s_name, s_ratio in stats['actual_ratios'].items():
            target_ratio = distribution.get(s_name, 0)
            doc_content += f"  - `{s_name}`: {s_ratio*100:.2f}% (目标: {target_ratio*100:.2f}%)\n"
        doc_content += "\n"

    # 分布一致性分析
    doc_content += "### 分布一致性分析\n\n"
    
    for scenario in distribution.keys():
        ratios = [res['actual_ratios'].get(scenario, 0) for res in validation_results['splits_stats'].values()]
        doc_content += f"场景 `{scenario}` 在各数据集中的比例: "
        doc_content += ", ".join([f"{r*100:.2f}%" for r in ratios])
        doc_content += "\n"

    doc_content += f"\n**平均方差**: {validation_results['distribution_variance']:.6f}\n"
    
    if validation_results["iid_compliance"]:
        doc_content += "\n✅ **结论**: 数据集满足IID假设，各数据集分布一致，可以安全用于机器学习训练和评估。\n"
    else:
        doc_content += "\n⚠️ **结论**: 数据集不满足IID假设，建议检查蓝图配置或原始数据生成过程。\n"
    
    doc_content += """
## 文件结构

### 原始数据 (`data_gen/rar_data`)
```
data_gen/rar_data/
├── easy/
│   ├── batch_0.json
│   └── ...
├── medium/
│   └── ...
└── hard/
    └── ...
```

### 最终数据集 (`data_gen/datasets`)
```
data_gen/datasets/
├── train/
│   ├── batch_0.json
│   └── ...
├── validation/
│   └── ...
└── test/
    └── ...
```

---
*此报告由数据生成器自动生成*
"""
    
    # 保存报告
    report_path = os.path.join(output_base_dir, 'iid_compliance_report.md')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(doc_content)
    
    print(f"IID合规性报告已保存到: {report_path}")


# 主函数 - 完全由蓝图驱动
if __name__ == "__main__":
    
    print("蓝图驱动的数据生成器启动")
    print("=" * 50)
    
    # 蓝图文件路径是固定的
    blueprint_file = "campaign.yaml"
    
    if not os.path.exists(blueprint_file):
        print(f"错误: 蓝图文件 '{blueprint_file}' 不存在。")
        print("请创建一个 campaign.yaml 文件来定义生成和划分计划。")
    else:
        # 从蓝图生成数据集
        generate_dataset_from_blueprint(blueprint_file)
    
    print("\n数据生成完成！")