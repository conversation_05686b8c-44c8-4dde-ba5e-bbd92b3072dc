import os
import json
import random
import numpy as np
import shutil
import math


def stratified_split_batches(files_by_scenario, dist, num_train, num_val, num_test):
    """
    使用分层采样，根据设定的场景分布比例来划分数据集。
    V4: 采用精确的比例分配算法，处理舍入误差。
    """
    # 复制文件列表以进行安全的pop操作
    pools = {s: list(f) for s, f in files_by_scenario.items()}
    
    # --- 1. 计算每个数据集在每个场景下的理想批次数 ---
    plan = {'train': {}, 'validation': {}, 'test': {}}
    datasets = {'train': num_train, 'validation': num_val, 'test': num_test}

    for scenario, pool in pools.items():
        # 这个场景可用的总批次数
        available_count = len(pool)
        
        # 按比例分配整数部分
        allocated_count = 0
        for name, total_size in datasets.items():
            count = int(total_size * dist[scenario])
            plan[name][scenario] = count
            allocated_count += count
            
        # 处理因取整而未分配的批次
        remaining_to_allocate = available_count - allocated_count
        if remaining_to_allocate > 0:
            # 计算小数部分的需求
            remainders = {
                name: (total_size * dist[scenario]) - plan[name][scenario]
                for name, total_size in datasets.items()
            }
            # 按小数需求降序排序
            sorted_by_remainder = sorted(remainders.keys(), key=lambda n: remainders[n], reverse=True)
            
            # 将剩余的批次逐一分配给小数需求最大的数据集
            for i in range(remaining_to_allocate):
                dataset_to_give = sorted_by_remainder[i % len(sorted_by_remainder)]
                plan[dataset_to_give][scenario] += 1
    
    # --- 2. 修正分配总数，确保与请求完全一致 ---
    for name, total_size in datasets.items():
        current_total = sum(plan[name].values())
        discrepancy = current_total - total_size
        
        # 如果分配多了，从分配最多的场景中减去
        while discrepancy > 0:
            max_scenario = max(plan[name], key=lambda s: plan[name][s])
            if plan[name][max_scenario] > 0:
                plan[name][max_scenario] -= 1
                discrepancy -= 1
            else: break
            
        # 如果分配少了，从资源最充足的场景中增加
        while discrepancy < 0:
            # 找到既能提供资源，又在该数据集中分配比例相对较低的场景
            best_scenario_to_add = max(pools.keys(), key=lambda s: len(pools[s]) - sum(d[s] for d in plan.values()))
            plan[name][best_scenario_to_add] += 1
            discrepancy += 1


    # --- 3. 执行分配 ---
    train_files, val_files, test_files = [], [], []
    
    for name, target_list in [('train', train_files), 
                              ('validation', val_files), 
                              ('test', test_files)]:
        
        print(f"为 {name} 数据集按计划分配: {plan[name]}")
        
        for scenario, num_to_sample in plan[name].items():
            # 安全检查
            if len(pools[scenario]) < num_to_sample:
                print(f"警告: 规划后 {scenario} 场景依然不足。需要{num_to_sample}, 可用{len(pools[scenario])}")
                num_to_sample = len(pools[scenario])

            sampled = random.sample(pools[scenario], num_to_sample)
            target_list.extend(sampled)
            pools[scenario] = [f for f in pools[scenario] if f not in sampled]

        random.shuffle(target_list)

    print("\n分层采样划分完成:")
    print(f"  训练集: {len(train_files)} 个批次")
    print(f"  验证集: {len(val_files)} 个批次")
    print(f"  测试集: {len(test_files)} 个批次")

    return {"train": train_files, "validation": val_files, "test": test_files}


def move_batches_to_splits(temp_dir, splits_dict, output_base_dir):
    """将批次文件从临时目录移动到最终目录结构
    
    Args:
        temp_dir (str): 临时目录路径
        splits_dict (dict): 划分后的文件列表字典
        output_base_dir (str): 最终输出基础目录
        
    Returns:
        dict: 移动后的文件统计
    """
    # 创建最终目录结构
    for split_name in splits_dict.keys():
        os.makedirs(f"{output_base_dir}/{split_name}", exist_ok=True)
    
    moved_stats = {}
    
    for split_name, file_list in splits_dict.items():
        moved_count = 0
        
        for new_idx, filename in enumerate(file_list):
            # 源文件路径
            src_path = f"{temp_dir}/{filename}"
            
            # 目标文件路径 - 重新编号
            dst_path = f"{output_base_dir}/{split_name}/batch_{new_idx}.json"
            
            if os.path.exists(src_path):
                shutil.move(src_path, dst_path)
                moved_count += 1
            else:
                print(f"警告: 源文件不存在: {src_path}")
        
        moved_stats[split_name] = moved_count
        print(f"移动 {split_name} 数据集: {moved_count} 个批次")
    
    return moved_stats


def validate_iid_compliance(output_base_dir, splits_dict, scenario_map, target_dist):
    """验证IID合规性 - 检查三个数据集的分布与目标分布的一致性"""
    validation_results = {
        "splits_stats": {},
        "scenario_ratios": {},
        "iid_compliance": True,
        "distribution_variance": 0.0,
        "distribution_error": 0.0
    }
    
    print("\n=== IID合规性验证 (分层采样) ===")
    
    all_scenario_ratios = []

    for split_name, file_list in splits_dict.items():
        split_dir = f"{output_base_dir}/{split_name}"
        
        total_batches = len(file_list)
        scenario_counts = {"easy": 0, "medium": 0, "hard": 0}
        total_flows = 0
        
        for batch_idx, original_filename in enumerate(file_list):
            batch_file = f"{split_dir}/batch_{batch_idx}.json"
            
            # 确定场景
            scenario = scenario_map.get(original_filename, "unknown")
            if scenario != "unknown":
                scenario_counts[scenario] += 1
                
            if os.path.exists(batch_file):
                with open(batch_file, 'r') as f:
                    batch_data = json.load(f)
                total_flows += len(batch_data.get("flows", []))
        
        scenario_ratios_in_split = {s: c / total_batches if total_batches > 0 else 0 for s, c in scenario_counts.items()}
        all_scenario_ratios.append(list(scenario_ratios_in_split.values()))

        validation_results["splits_stats"][split_name] = {
            "total_batches": total_batches,
            "total_flows": total_flows,
            "scenario_counts": scenario_counts,
            "scenario_ratios": scenario_ratios_in_split
        }
        
        validation_results["scenario_ratios"][split_name] = scenario_ratios_in_split
        
        print(f"{split_name.upper()} 数据集:")
        print(f"  总批次数: {total_batches}")
        print(f"  总流数: {total_flows}")
        print(f"  场景分布 (实际): {scenario_counts}")
        target_counts = {s: round(total_batches * p) for s, p in target_dist.items()}
        print(f"  场景分布 (目标): Easy={target_dist['easy']:.0%}, Medium={target_dist['medium']:.0%}, Hard={target_dist['hard']:.0%}")

    # 计算分布误差 (与目标分布的平均绝对误差)
    total_error = 0
    num_splits = len(all_scenario_ratios)
    if num_splits > 0:
        target_ratios = np.array(list(target_dist.values()))
        for ratio_set in all_scenario_ratios:
            total_error += np.mean(np.abs(np.array(ratio_set) - target_ratios))
        
        mean_abs_error = total_error / num_splits
        validation_results["distribution_error"] = mean_abs_error
        
        print(f"\n分布一致性分析:")
        print(f"  平均绝对误差 (与目标分布): {mean_abs_error:.4f}")
        
        if mean_abs_error > 0.1: # 允许10%的误差 (主要来自小样本量的四舍五入)
            validation_results["iid_compliance"] = False
            print(f"  ⚠️  IID合规性检查失败: 分布误差过大 ({mean_abs_error:.4f} > 0.1)")
        else:
            print(f"  ✅ IID合规性检查通过: 实际分布与目标分布基本一致")
    
    return validation_results


def _generate_iid_compliance_report(output_base_dir, validation_results, params):
    """生成IID合规性报告"""
    
    doc_content = f"""# IID合规性数据生成报告

## 概述
此文档记录了数据集的IID (Independent and Identically Distributed) 合规性验证结果。
生成的数据集旨在覆盖 Easy, Medium, Hard 三种负载场景，并确保它们在训练、验证、测试集中分布均匀。

## 数据生成流程
1. **场景化批次生成**: 根据场景配置，按比例生成 easy, medium, hard 批次到临时目录。
2. **全局随机打乱**: 使用 `random.shuffle()` 统一打乱所有生成的批次文件。
3. **按比例划分**: 将打乱后的批次分配到 `train`, `validation`, `test` 数据集。
4. **文件重组与验证**: 将文件移动到最终目录并重新编号，最后进行IID合规性验证。

## 配置参数
- **总批次数**: {params.get("num_train", 0) + params.get("num_validation", 0) + params.get("num_test", 0)}
  - **训练集**: {params.get("num_train", 0)}
  - **验证集**: {params.get("num_validation", 0)}
  - **测试集**: {params.get("num_test", 0)}
- **场景比例 (目标)**: {params.get("scenario_distribution")}

## 验证结果

### 整体统计
"""
    
    # IID合规性状态
    compliance_status = "✅ 通过" if validation_results["iid_compliance"] else "❌ 失败"
    doc_content += f"- **IID合规性**: {compliance_status}\n"
    doc_content += f"- **场景比例均值方差**: {validation_results['distribution_variance']:.6f}\n\n"
    
    # 详细的数据集统计
    doc_content += "### 各数据集详细统计\n\n"
    
    for split_name, stats in validation_results["splits_stats"].items():
        doc_content += f"#### {split_name.upper()} 数据集\n"
        doc_content += f"- 总批次数: {stats['total_batches']}\n"
        doc_content += f"- 总流数: {stats['total_flows']}\n"
        doc_content += f"- 场景分布: {stats['scenario_counts']}\n"
        doc_content += f"- 场景比例: Easy={stats['scenario_ratios']['easy']:.1%}, Medium={stats['scenario_ratios']['medium']:.1%}, Hard={stats['scenario_ratios']['hard']:.1%}\n\n"
    
    # 分布一致性分析
    doc_content += "### 分布一致性分析\n\n"
    doc_content += "各数据集场景比例对比:\n"
    for split_name, ratios in validation_results["scenario_ratios"].items():
        doc_content += f"- {split_name}: Easy={ratios['easy']:.2%}, Medium={ratios['medium']:.2%}, Hard={ratios['hard']:.2%}\n"
    
    if validation_results["iid_compliance"]:
        doc_content += "\n✅ **结论**: 数据集满足IID假设，各数据集场景分布一致，可以安全用于机器学习训练和评估。\n"
    else:
        doc_content += "\n⚠️ **结论**: 数据集不满足IID假设，建议重新生成数据或调整参数。\n"
    
    # 保存报告
    with open(f'{output_base_dir}/iid_compliance_report.md', 'w', encoding='utf-8') as f:
        f.write(doc_content)
    
    print(f"IID合规性报告已保存到: {output_base_dir}/iid_compliance_report.md") 