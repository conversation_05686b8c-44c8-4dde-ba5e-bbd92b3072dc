#!/usr/bin/env python3
#clos_topo.py   
import argparse
import networkx as nx
import ipaddress
import sys
import networkx.drawing.nx_agraph
from jinja2 import Environment, FileSystemLoader
from typing import List, Dict, Tuple
from collections import defaultdict
import itertools
import torch


class Clos():
    def __init__(self, tier2_size, tier1_size, host_size_each, ps_bandwidth=400, sh_bandwidth=100):
        # 初始化Clos网络的参数
        self.G = nx.DiGraph()  # 创建一个有向图来表示网络
        self.nodes = {}  # 用于存储不同类型的节点
        self.edges = []  # 用于存储边（连接）
        
        # 【新增】用于链路图的属性
        self.link_to_idx: Dict[Tuple[str, str], int] = {}
        self.idx_to_link: Dict[int, Tuple[str, str]] = {}
        # 【已修改】重命名属性以匹配train.py中的调用
        self.edge_index: torch.Tensor = torch.empty((2, 0), dtype=torch.long)
        
        self.tier2_size = tier2_size
        self.tier1_size = tier1_size
        self.host_size_each = host_size_each
        self.host_size = host_size_each * tier1_size
        self.total_nodes = int(tier2_size) + int(tier1_size) + int(self.host_size)
        self.image_name = 'clos.png'

        # 定义不同层次间的带宽
        self.ps_bandwidth = ps_bandwidth
        self.sh_bandwidth = sh_bandwidth

    def find_all_paths(self, source: str, target: str, cutoff=None) -> List[List[str]]:
        """
        找到两个主机之间的所有可能路径
        """
        if not source.startswith('H') or not target.startswith('H'):
            raise ValueError(f"源和目标必须是主机 (例如 'H1', 'H5'), 但收到: {source}, {target}")

        if source == target:
            return [[source]]

        if source not in self.G or target not in self.G:
            raise ValueError(f"节点 {source} 或 {target} 不在拓扑图中")

        try:
            all_paths = list(nx.all_simple_paths(self.G, source, target, cutoff=cutoff))
            all_paths.sort(key=len)
            return all_paths
        except Exception as e:
            print(f"查找路径时出错: {e}")
            return []
            
    def build(self):
        """
        构建完整的Clos拓扑并准备好链路图所需的所有数据结构。
        """
        # --- 1. 构建物理拓扑 ---
        self.nodes['P'] = [f'P{node + 1}' for node in range(int(self.tier2_size))]
        self.nodes['S'] = [f'S{node + 1}' for node in range(int(self.tier1_size))]
        self.nodes['H'] = [f'H{node + 1}' for node in range(int(self.host_size))]
        
        node_index = 1
        for node_lists, nodes in self.nodes.items():
            node_type = [1, 0, 0] if node_lists == 'P' else ([0, 1, 0] if node_lists == 'S' else [0, 0, 1])
            for node in nodes:
                self.G.add_node(node, idx=node_index, type=node_type)
                node_index += 1

        for p_router in self.nodes['P']:
            for s_router in self.nodes['S']:
                self.edges.append((p_router, s_router, 'PS'))
                self.edges.append((s_router, p_router, 'SP'))

        host_index_start = 0
        for s_router in self.nodes['S']:
            for host_index in range(host_index_start, host_index_start + self.host_size_each):
                self.edges.append((s_router, f'H{1 + host_index}', 'SH'))
                self.edges.append((f'H{1 + host_index}', s_router, 'HS'))
            host_index_start += self.host_size_each

        for i, (source, target, edge_type) in enumerate(self.edges):
            self.G.add_edge(source, target)
            bandwidth = self.ps_bandwidth if edge_type in ['PS', 'SP'] else self.sh_bandwidth
            nx.set_edge_attributes(self.G, {(source, target): {'idx': i + 1, 'btw': bandwidth, 'type': edge_type}})

        print(f'Rendered {node_index - 1} Nodes and {len(self.edges)} Edges')
        
        # --- 2. 【新增】构建链路图相关的数据结构 ---
        self._build_link_mapping()
        self._build_link_graph_edges()

    def _build_link_mapping(self):
        """
        创建链路到索引的双向映射。
        """
        self.link_to_idx.clear()
        self.idx_to_link.clear()
        for i, (src, dst) in enumerate(self.G.edges()):
            self.link_to_idx[(src, dst)] = i
            self.idx_to_link[i] = (src, dst)
        print(f"创建了包含 {len(self.link_to_idx)} 条链路的映射。")
    
    def _build_link_graph_edges(self):
        """
        【新增】构建链路图的边索引 (edge_index)。
        逻辑：如果两条链路连接到同一个交换机，则它们之间有边。
        """
        edge_list = []
        switch_to_links = defaultdict(list)

        for link_idx, (src, dst) in self.idx_to_link.items():
            if src.startswith(('S', 'P')):
                switch_to_links[src].append(link_idx)
            if dst.startswith(('S', 'P')):
                switch_to_links[dst].append(link_idx)

        for _, connected_link_indices in switch_to_links.items():
            if len(connected_link_indices) > 1:
                for u, v in itertools.permutations(connected_link_indices, 2):
                    edge_list.append([u, v])

        if edge_list:
            unique_edges = list(set(map(tuple, edge_list)))
            # 【已修改】将数据存入正确的属性名
            self.edge_index = torch.tensor(unique_edges, dtype=torch.long).t().contiguous()
        else:
            self.edge_index = torch.empty((2, 0), dtype=torch.long)
        print(f"【交换机中心式】链路图边数: {self.edge_index.size(1)}")
        
    def get_link_capacity(self, link):
        src, dst = link
        if self.G.has_edge(src, dst):
            return self.G.edges[src, dst]['btw']
        print(f"警告: 链路 {link} 在拓扑中不存在，返回默认带宽。")
        return self.ps_bandwidth

    def get_total_links(self) -> int:
        """返回图中单向链路的总数"""
        return len(self.G.edges())

    def get_path_link_indices(self, path: List[str]) -> List[int]:
        """
        将路径节点列表转换为链路索引列表。
        """
        if len(path) < 2:
            return []
            
        link_indices = []
        for i in range(len(path) - 1):
            link = (path[i], path[i + 1])
            if link in self.link_to_idx:
                link_indices.append(self.link_to_idx[link])
            else:
                raise ValueError(f"链路 {link} 在link_to_idx映射中不存在。请确保build()方法已被调用。")
        return link_indices

if __name__ == "__main__":
    clos_instance = Clos(4, 4, 4, ps_bandwidth=16000, sh_bandwidth=16000)
    clos_instance.build()
    print(f"Total links: {clos_instance.get_total_links()}")
    
    # 测试新增的方法
    sample_path = ['H1', 'S1', 'P1', 'S3', 'H5']
    try:
        if clos_instance.G.has_node('H1') and clos_instance.G.has_node('S1'):
             indices = clos_instance.get_path_link_indices(sample_path)
             print(f"路径 {sample_path} 的链路索引为: {indices}")
        else:
            print("示例路径中的节点不存在于构建的拓扑中。")
    except ValueError as e:
        print(e)
