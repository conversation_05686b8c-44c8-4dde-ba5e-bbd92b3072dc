#!/usr/bin/env python3
"""
inference.py

This script loads a trained STGNN model and performs autoregressive inference
on a given set of flows. It simulates the passing of time, predicting the FCT
for each flow sequentially and using that prediction to inform subsequent ones.

USAGE:
    1. First train a model using train.py:
       python train.py --epochs 50 --lr 1e-4

    2. Run inference on test data:
       python inference.py --model_path stgnn_sim_model_optimized.pth --input_file datasets/test/batch_0.json

    3. Visualize inference results:
       python visualization.py --predictions_file inference_results_visualization.json --inference_mode

OUTPUT FILES:
    - inference_results.json: Detailed inference results with metadata
    - inference_results_visualization.json: Data formatted for visualization

EXAMPLE:
    python inference.py \
        --model_path stgnn_sim_model_optimized.pth \
        --input_file datasets/test/batch_0.json \
        --output_file my_inference_results.json
        
    python visualization.py \
        --predictions_file my_inference_results_visualization.json \
        --inference_mode \
        --output_dir inference_plots
"""
import torch
import numpy as np
import json
import os
import logging
import argparse
import time
from typing import List, Dict
from torch_geometric.data import Data, Batch

# 从model.py导入复杂模型组件（推理时使用动态特征提取）
from model import (
    STGNNModel, SGNNModule, FutureEncoder, BiLSTMAggregationLayer,
    FUTURE_WINDOW, TIME_SLICES, SPATIAL_EMBEDDING_DIM, 
    FUTURE_EMBEDDING_DIM, HIDDEN_DIM, NUM_GNN_LAYERS, DEVICE,
    LinkGraphBuilder, FeatureEngineer
)
# 不再需要导入高效模型组件，只使用原始STGNNModel

from clos_topo import Clos

# ==================== 指标计算函数 ====================
def compute_metrics(predictions, targets):
    """
    计算MAE、RMSE、MAPE三个指标
    
    Args:
        predictions: 预测值列表
        targets: 真实值列表
        
    Returns:
        Dict包含mae, rmse, mape指标
    """
    predictions = np.array(predictions)
    targets = np.array(targets)
    
    # MAE (Mean Absolute Error)
    mae = np.mean(np.abs(predictions - targets))
    
    # RMSE (Root Mean Square Error) 
    rmse = np.sqrt(np.mean((predictions - targets) ** 2))
    
    # MAPE (Mean Absolute Percentage Error)
    # 避免除零错误，当真实值为0时跳过
    non_zero_mask = targets != 0
    if np.sum(non_zero_mask) > 0:
        mape = np.mean(np.abs((targets[non_zero_mask] - predictions[non_zero_mask]) / targets[non_zero_mask])) * 100
    else:
        mape = float('inf')  # 如果所有真实值都为0，MAPE无意义
    
    return {
        'mae': mae,
        'rmse': rmse,
        'mape': mape
    }

def setup_logging():
    """设置日志记录"""
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', 
                       handlers=[logging.StreamHandler()])

def parse_arguments() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='使用训练好的STGNN模型进行FCT预测推理')
    parser.add_argument('--model_path', type=str, default='best_model.pth', 
                       help='训练好的模型检查点路径(.pth)')
    parser.add_argument('--input_file', type=str, default='data_gen/datasets/test/batch_0.json', 
                       help='包含待预测流的输入JSON文件路径')
    parser.add_argument('--output_file', type=str, default='inference_results.json', 
                       help='推理结果保存路径')
    

    
    
    return parser.parse_args()

def run_batch_inference_simulation(model, flows_to_predict: List[Dict]) -> List[Dict]:
    """
    执行批量推理仿真（使用优化后的批量处理）
    
    Args:
        model: 训练好的STGNNModel实例
        flows_to_predict: 按start_time排序的流列表
    
    Returns:
        包含预测FCT的流列表
    """
    model.eval()
    
    start_simulation_time = time.time()
    
    with torch.no_grad():
        # 直接进行批量预测
        batch_data = {
            'flows': flows_to_predict,
            'all_flows': flows_to_predict
        }
        
        try:
            # 批量模型预测
            log_predicted_fcts = model(batch_data)
            predicted_fcts = torch.expm1(log_predicted_fcts.squeeze()).cpu().numpy()
            
            # 构建结果
            completed_flows = []
            for i, flow_to_predict in enumerate(flows_to_predict):
                if i < len(predicted_fcts):
                    processed_flow = flow_to_predict.copy()
                    processed_flow['predicted_fct'] = float(predicted_fcts[i])
                    processed_flow['time_delay'] = float(predicted_fcts[i])  # 为了兼容性
                    completed_flows.append(processed_flow)
                    
            logging.info(f"批量预测完成，成功处理了 {len(completed_flows)} 个流")
            
        except Exception as e:
            logging.error(f"批量推理失败，回退到逐流处理: {e}")
            # 回退到原始逐流处理
            return run_inference_simulation(model, flows_to_predict)
                
    end_simulation_time = time.time()
    logging.info(f"批量推理仿真完成，处理了 {len(completed_flows)} 个流，耗时 {end_simulation_time - start_simulation_time:.2f} 秒")
    
    return completed_flows

def run_inference_simulation(model, flows_to_predict: List[Dict]) -> List[Dict]:
    """
    执行自回归推理仿真（使用动态特征提取）
    
    Args:
        model: 训练好的STGNNModel实例
        flows_to_predict: 按start_time排序的流列表
    
    Returns:
        包含预测FCT的流列表
    """
    model.eval()
    
    # 存储已处理的流及其预测结果
    completed_flows = []
    
    start_simulation_time = time.time()
    
    with torch.no_grad():
        for i, flow_to_predict in enumerate(flows_to_predict):
            current_time = flow_to_predict['inputs']['start_time']
            
            try:
                # 准备批次数据
                batch_data = {
                    'flows': [flow_to_predict],
                    'all_flows': flows_to_predict  # 使用所有流作为上下文
                }
                
                # 模型预测
                log_predicted_fct = model(batch_data)
                predicted_fct = torch.expm1(log_predicted_fct.squeeze()).item()
                
                # 后处理和状态更新
                processed_flow = flow_to_predict.copy()
                processed_flow['predicted_fct'] = predicted_fct
                processed_flow['time_delay'] = predicted_fct  # 为了兼容性
                
                completed_flows.append(processed_flow)
                
                if (i + 1) % 10 == 0:
                    logging.info(f"已预测 {i+1}/{len(flows_to_predict)} 个流... 最新FCT: {predicted_fct:.4f}s")
                    
            except (ValueError, KeyError) as e:
                logging.warning(f"跳过流 {flow_to_predict['inputs']['flow_id']}，错误: {e}")
                continue
                
    end_simulation_time = time.time()
    logging.info(f"推理仿真完成，处理了 {len(completed_flows)} 个流，耗时 {end_simulation_time - start_simulation_time:.2f} 秒")
    
    return completed_flows


def main():
    setup_logging()
    args = parse_arguments()
    logging.info(f"开始推理，参数: {args}")

    # 1. 构建拓扑
    logging.info("构建网络拓扑...")
    clos = Clos(4, 4, 4, ps_bandwidth=16000, sh_bandwidth=16000)  # 这些参数应与训练时匹配
    clos.build()
    
    # 2. 加载模型检查点和初始化组件
    logging.info("加载训练好的模型...")
    try:
        checkpoint = torch.load(args.model_path, map_location=torch.device('cuda' if torch.cuda.is_available() else 'cpu'), weights_only=False)
        logging.info(f"成功从 {args.model_path} 加载检查点")
        
        # 读取元数据和超参数
        metadata = checkpoint.get('metadata', {})
        global_mean = metadata.get('global_mean', 0.0)
        global_std = metadata.get('global_std', 1.0)
        hyperparams = checkpoint.get('hyperparams', {
            'spatial_embedding_dim': SPATIAL_EMBEDDING_DIM,
            'future_embedding_dim': FUTURE_EMBEDDING_DIM,
            'hidden_dim': HIDDEN_DIM,
            'num_gnn_layers': NUM_GNN_LAYERS,
        })
        
        logging.info(f"加载的全局统计信息: mean={global_mean:.2f}, std={global_std:.2f}")
        
    except FileNotFoundError:
        logging.error(f"在 {args.model_path} 找不到模型文件，请提供有效路径")
        return
    except Exception as e:
        logging.error(f"加载模型检查点时出错: {e}")
        return

    # 3. 初始化原始STGNNModel（现在只有一种模型架构）
    logging.info("使用原始STGNNModel进行推理...")
    model = STGNNModel(clos, **hyperparams).to(DEVICE)
    model.load_state_dict(checkpoint['model_state_dict'])
    logging.info(f"模型参数总数: {sum(p.numel() for p in model.parameters()):,}")
    
    # 4. 更新模型的特征工程器统计信息
    if hasattr(model, 'feature_engineer'):
        model.feature_engineer.global_mean = global_mean
        model.feature_engineer.global_std = global_std
    
    # 5. 加载和准备输入数据
    logging.info(f"从 {args.input_file} 加载输入数据...")
    try:
        with open(args.input_file, 'r') as f:
            input_data = json.load(f)
        
        # 按start_time排序流以准备仿真
        flows_to_predict = sorted(input_data['flows'], key=lambda x: x['inputs']['start_time'])
        logging.info(f"加载并排序了 {len(flows_to_predict)} 个流用于推理")
    except Exception as e:
        logging.error(f"加载或解析输入文件失败: {e}")
        return

    # 6. 运行推理（使用优化后的批量处理）
    logging.info("运行批量推理...")
    results = run_batch_inference_simulation(model, flows_to_predict)

    # 7. 准备可视化数据和计算指标
    predictions = []
    actuals = []
    
    for result in results:
        predictions.append(result['predicted_fct'])
        
        # 查找原始流中的真实time_delay
        original_flow = next((f for f in flows_to_predict 
                            if f['inputs']['flow_id'] == result['inputs']['flow_id']), None)
        if original_flow and 'time_delay' in original_flow:
            actuals.append(original_flow['time_delay'])
        else:
            actuals.append(result['predicted_fct'])  # 如果没有真实值，使用预测值
    
    # 8. 计算并记录评估指标
    if len(predictions) > 0 and len(actuals) > 0:
        # 过滤掉预测值作为真实值的情况
        valid_indices = [i for i, (pred, actual) in enumerate(zip(predictions, actuals)) if pred != actual]
        
        if len(valid_indices) > 0:
            valid_predictions = [predictions[i] for i in valid_indices]
            valid_actuals = [actuals[i] for i in valid_indices]
            
            metrics = compute_metrics(valid_predictions, valid_actuals)
            logging.info("="*50)
            logging.info("推理评估指标:")
            logging.info(f"MAE (平均绝对误差): {metrics['mae']:.6f}")
            logging.info(f"RMSE (均方根误差): {metrics['rmse']:.6f}")
            logging.info(f"MAPE (平均绝对百分比误差): {metrics['mape']:.2f}%")
            logging.info(f"有效样本数: {len(valid_predictions)}")
            logging.info("="*50)
        else:
            metrics = {'mae': 0.0, 'rmse': 0.0, 'mape': 0.0}
            logging.info("没有足够的有效样本来计算指标")
    else:
        metrics = {'mae': 0.0, 'rmse': 0.0, 'mape': 0.0}
        logging.info("没有数据可以计算指标")

    # 9. 保存可视化数据（包含指标）
    visualization_data = {
        'predictions': predictions,
        'actuals': actuals,
        'metrics': metrics,  # 包含MAE, RMSE, MAPE指标
        'inference_metadata': {
            'num_samples': len(results),
            'input_file': args.input_file,
            'model_path': args.model_path,
            'inference_timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
    }
    
    # 保存可视化数据到专门的文件
    visualization_file = args.output_file.replace('.json', '_visualization.json')
    with open(visualization_file, 'w') as f:
        json.dump(visualization_data, f, indent=4)
    logging.info(f"可视化数据已保存至 {visualization_file}")

    # 10. 保存结果（包含指标）
    output_results = {
        'inference_summary': {
            'total_flows': len(results),
            'input_file': args.input_file,
            'model_path': args.model_path,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'evaluation_metrics': metrics  # 添加评估指标
        },
        'predictions': [
            {
                "flow_id": r['inputs']['flow_id'],
                "start_time": r['inputs']['start_time'],
                "predicted_fct": r['predicted_fct'],
                "actual_fct": next((f.get('time_delay') for f in flows_to_predict 
                                  if f['inputs']['flow_id'] == r['inputs']['flow_id']), None)
            } for r in results
        ]
    }
    
    with open(args.output_file, 'w') as f:
        json.dump(output_results, f, indent=4)
    
    logging.info(f"推理结果已保存至 {args.output_file}")
    logging.info(f"可视化结果请运行: python visualization.py --predictions_file {visualization_file}")
    logging.info("推理脚本成功完成")

if __name__ == "__main__":
    main()
