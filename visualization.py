#!/usr/bin/env python3
"""
visualization.py

This script is used to visualize the training results generated by train.py.
It can plot training/validation loss curves and comparison charts of predicted vs. actual values.
"""
import json
import matplotlib.pyplot as plt
import numpy as np
import argparse
import os
from typing import Dict, Optional

# Set plot style
plt.style.use('seaborn-v0_8')

def plot_loss_curve(history_data: dict, save_path: str):
    """
    Plots and saves the training and validation loss curve.

    Args:
        history_data (dict): A dictionary containing 'train_loss' and 'val_loss' lists.
        save_path (str): The path to save the image.
    """
    epochs = range(1, len(history_data['train_loss']) + 1)
    
    plt.figure(figsize=(12, 7))
    plt.plot(epochs, history_data['train_loss'], 'o-', color='#1f77b4', linewidth=2.5, 
             markersize=6, markeredgecolor='white', markeredgewidth=1, label='Training Loss')
    plt.plot(epochs, history_data['val_loss'], 's-', color='#d62728', linewidth=2.5, 
             markersize=6, markeredgecolor='white', markeredgewidth=1, label='Validation Loss')
    
    plt.title('Model Training and Validation Loss Curve', fontsize=17, fontweight='bold')
    plt.xlabel('Epochs', fontsize=13)
    plt.ylabel('MSE Loss (Log Scale)', fontsize=13)
    plt.xticks(fontsize=11)
    plt.yticks(fontsize=11)
    plt.legend(fontsize=14, framealpha=0.9, fancybox=True, shadow=True)
    plt.grid(True, alpha=0.3, linestyle=':', linewidth=0.5)
    
    # Ensure the save directory exists
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    plt.savefig(save_path, dpi=300)
    print(f"Loss curve plot saved to: {save_path}")
    plt.close()

def plot_predictions_vs_actuals(prediction_data: dict, save_path: str):
    """
    Plots a scatter plot of predicted vs. actual values.

    Args:
        prediction_data (dict): A dictionary containing 'predictions' and 'actuals' lists.
        save_path (str): The path to save the image.
    """
    preds = np.array(prediction_data['predictions'])
    actuals = np.array(prediction_data['actuals'])

    plt.figure(figsize=(10, 10))
    # Plot scatter with enhanced styling
    plt.scatter(actuals, preds, alpha=0.6, s=30, color='#1f77b4', edgecolors='white', 
               linewidths=0.5, label='Predicted vs. Actual')
    
    # Plot y=x diagonal line for reference
    lims = [
        np.min([plt.xlim(), plt.ylim()]),  # min of both axes
        np.max([plt.xlim(), plt.ylim()]),  # max of both axes
    ]
    plt.plot(lims, lims, '--', color='#d62728', alpha=0.8, linewidth=2.5, zorder=0, 
            label='Perfect Prediction (y=x)')
    plt.xlim(lims)
    plt.ylim(lims)
    
    # Calculate statistics
    rmse = np.sqrt(np.mean((preds - actuals) ** 2))
    mae = np.mean(np.abs(preds - actuals))
    r2 = np.corrcoef(preds, actuals)[0, 1] ** 2 if len(preds) > 1 else 0
    
    # Add statistics text box
    stats_text = f'RMSE: {rmse:.4f}\nMAE: {mae:.4f}\nR²: {r2:.4f}'
    plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, fontsize=12,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    plt.title('Predicted FCT vs. Actual FCT (Test Set)', fontsize=17, fontweight='bold')
    plt.xlabel('Actual FCT (seconds)', fontsize=13)
    plt.ylabel('Predicted FCT (seconds)', fontsize=13)
    plt.xticks(fontsize=11)
    plt.yticks(fontsize=11)
    plt.legend(fontsize=14, loc='best', framealpha=0.9, fancybox=True, shadow=True)
    plt.grid(True, alpha=0.3, linestyle=':', linewidth=0.5)
    plt.gca().set_aspect('equal', adjustable='box') # Keep x,y axes to the same scale
    
    # Ensure the save directory exists
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    plt.savefig(save_path, dpi=300)
    print(f"Prediction comparison plot saved to: {save_path}")
    plt.close()

def plot_error_distribution(prediction_data: dict, save_path: str):
    """
    Plots a histogram of the prediction errors.

    Args:
        prediction_data (dict): A dictionary containing 'predictions' and 'actuals' lists.
        save_path (str): The path to save the image.
    """
    preds = np.array(prediction_data['predictions'])
    actuals = np.array(prediction_data['actuals'])
    errors = preds - actuals

    plt.figure(figsize=(12, 7))
    plt.hist(errors, bins=50, alpha=0.7, color='#2ca02c', edgecolor='white', linewidth=0.5)
    
    mean_error = np.mean(errors)
    std_error = np.std(errors)
    
    plt.axvline(mean_error, color='#d62728', linestyle='--', linewidth=2.5, 
               label=f'Mean Error: {mean_error:.4f}')
    plt.axvline(mean_error + std_error, color='#ff7f0e', linestyle=':', linewidth=2, 
               label=f'+1 Std: {mean_error + std_error:.4f}')
    plt.axvline(mean_error - std_error, color='#ff7f0e', linestyle=':', linewidth=2, 
               label=f'-1 Std: {mean_error - std_error:.4f}')
    
    plt.title(f'Prediction Error Distribution (Mean={mean_error:.4f}, Std={std_error:.4f})', 
             fontsize=17, fontweight='bold')
    plt.xlabel('Prediction Error (seconds)', fontsize=13)
    plt.ylabel('Frequency', fontsize=13)
    plt.xticks(fontsize=11)
    plt.yticks(fontsize=11)
    plt.legend(fontsize=12, framealpha=0.9, fancybox=True, shadow=True)
    plt.grid(True, alpha=0.3, linestyle=':', linewidth=0.5)
    
    # Ensure the save directory exists
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    plt.savefig(save_path, dpi=300)
    print(f"Error distribution plot saved to: {save_path}")
    plt.close()

# ==================== 【新增】新函数 ====================
def plot_predictions_timeseries(prediction_data: Dict, save_path: str, epoch: Optional[int] = None, show_values: bool = True, value_step: Optional[int] = None):
    """
    Plots a bar chart of predicted vs. actual values over samples.

    Args:
        prediction_data (dict): A dictionary containing 'predictions' and 'actuals' lists.
        save_path (str): The path to save the image.
        epoch (Optional[int]): The current epoch number to include in the title.
        show_values (bool): Whether to display value labels on data points.
        value_step (Optional[int]): Step for displaying value labels. If None, uses intelligent strategy.
    """
    preds = np.array(prediction_data['predictions'])
    actuals = np.array(prediction_data['actuals'])
    num_samples = len(actuals)
    sample_indices = np.arange(num_samples)
    bar_width = 0.35

    plt.figure(figsize=(15, 8))
    plt.bar(sample_indices - bar_width/2, actuals, bar_width, label='Actual Values (Ground Truth)', 
            color='#1f77b4', alpha=0.8)
    plt.bar(sample_indices + bar_width/2, preds, bar_width, label='Predicted Values (Model Output)', 
            color='#d62728', alpha=0.8)
    
    # Calculate statistics
    rmse = np.sqrt(np.mean((preds - actuals) ** 2))
    mae = np.mean(np.abs(preds - actuals))
    r2 = np.corrcoef(preds, actuals)[0, 1] ** 2 if len(preds) > 1 else 0
    
    # Determine step for label display first
    if show_values:
        if value_step is None:
            if num_samples <= 20:
                step = 1
            elif num_samples <= 50:
                step = 2
            elif num_samples <= 100:
                step = 5
            else:
                step = 10
        else:
            step = value_step
    else:
        step = 1  # Default value when not showing values
    
    # Add statistics text box
    stats_text = f'RMSE: {rmse:.4f}\nMAE: {mae:.4f}\nR²: {r2:.4f}'
    if show_values:
        stats_text += f'\nLabels: Every {step} samples'
    plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, fontsize=12,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    # Set Y-axis range with extra space for labels
    y_min = min(min(actuals), min(preds)) * 0.9
    y_max = max(max(actuals), max(preds)) * 1.15 # Increase top margin for labels
    plt.ylim(y_min, y_max)
    
    # Add value labels on data points
    if show_values:
        y_offset = (y_max - y_min) * 0.01
        
        for i in range(0, num_samples, step):
            actual_val = actuals[i]
            pred_val = preds[i]
            
            if actual_val < 0.001:
                actual_str = f'{actual_val:.1e}'
            else:
                actual_str = f'{actual_val:.2f}'

            if pred_val < 0.001:
                pred_str = f'{pred_val:.1e}'
            else:
                pred_str = f'{pred_val:.2f}'

            plt.text(i - bar_width/2, actual_val + y_offset, actual_str,
                    fontsize=8, ha='center', va='bottom', color='#1f77b4',
                    bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.7))
            plt.text(i + bar_width/2, pred_val + y_offset, pred_str,
                    fontsize=8, ha='center', va='bottom', color='#d62728',
                    bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.7))
    
    title = 'Predicted vs. Actual FCT'
    if epoch is not None:
        title += f' (After Epoch {epoch})'
    title += f' (Samples: {len(actuals)})'
    if show_values:
        title += f' - Values shown every {step} samples'
        
    plt.title(title, fontsize=17, fontweight='bold')
    plt.xlabel('Sample Index', fontsize=13)
    plt.ylabel('FCT (seconds)', fontsize=13)
    plt.xticks(sample_indices[::step], fontsize=11)
    plt.yticks(fontsize=11)
    plt.legend(fontsize=14, loc='upper right', framealpha=0.9, fancybox=True, shadow=True)
    plt.grid(True, alpha=0.3, linestyle=':', linewidth=0.5)
    
    # Adjust layout to accommodate labels
    plt.subplots_adjust(top=0.9, bottom=0.15)
    
    # Ensure the save directory exists
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    plt.savefig(save_path, dpi=300)
    print(f"Prediction timeseries plot saved to: {save_path}")
    plt.close()

def plot_best_epoch_predictions_timeseries(history_data: Dict, save_path: str, show_values: bool = True, value_step: Optional[int] = None):
    """
    Plots a bar chart of predicted vs. actual values for the best epoch only.

    Args:
        history_data (dict): A dictionary containing training history with best epoch predictions and targets.
        save_path (str): The path to save the image.
        show_values (bool): Whether to display value labels on data points.
        value_step (Optional[int]): Step for displaying value labels. If None, uses intelligent strategy.
    """
    if 'best_epoch_predictions' not in history_data or 'best_epoch_targets' not in history_data:
        print("Warning: No best epoch prediction data found in history. Skipping best epoch timeseries plot.")
        return
    
    preds = np.array(history_data['best_epoch_predictions'])
    actuals = np.array(history_data['best_epoch_targets'])
    best_epoch = history_data.get('best_epoch', 'Unknown')
    num_samples = len(actuals)
    sample_indices = np.arange(num_samples)
    bar_width = 0.35

    plt.figure(figsize=(15, 8))
    plt.bar(sample_indices - bar_width/2, actuals, bar_width, label='Actual Values (Ground Truth)', 
            color='#1f77b4', alpha=0.8)
    plt.bar(sample_indices + bar_width/2, preds, bar_width, label='Predicted Values (Model Output)', 
            color='#d62728', alpha=0.8)
    
    # Calculate statistics
    rmse = np.sqrt(np.mean((preds - actuals) ** 2))
    mae = np.mean(np.abs(preds - actuals))
    r2 = np.corrcoef(preds, actuals)[0, 1] ** 2 if len(preds) > 1 else 0
    
    # Determine step for label display first
    if show_values:
        if value_step is None:
            if num_samples <= 20:
                step = 1
            elif num_samples <= 50:
                step = 2
            elif num_samples <= 100:
                step = 5
            else:
                step = 10
        else:
            step = value_step
    else:
        step = 1  # Default value when not showing values
    
    # Add statistics text box
    stats_text = f'Best Epoch: {best_epoch}\nRMSE: {rmse:.4f}\nMAE: {mae:.4f}\nR²: {r2:.4f}'
    if show_values:
        stats_text += f'\nLabels: Every {step} samples'
    plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, fontsize=12,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    # Set Y-axis range with extra space for labels
    y_min = min(min(actuals), min(preds)) * 0.9
    y_max = max(max(actuals), max(preds)) * 1.15 # Increase top margin for labels
    plt.ylim(y_min, y_max)
    
    # Add value labels on data points
    if show_values:
        y_offset = (y_max - y_min) * 0.01
        
        for i in range(0, num_samples, step):
            actual_val = actuals[i]
            pred_val = preds[i]
            
            if actual_val < 0.001:
                actual_str = f'{actual_val:.1e}'
            else:
                actual_str = f'{actual_val:.2f}'

            if pred_val < 0.001:
                pred_str = f'{pred_val:.1e}'
            else:
                pred_str = f'{pred_val:.2f}'

            plt.text(i - bar_width/2, actual_val + y_offset, actual_str,
                    fontsize=8, ha='center', va='bottom', color='#1f77b4',
                    bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.7))
            plt.text(i + bar_width/2, pred_val + y_offset, pred_str,
                    fontsize=8, ha='center', va='bottom', color='#d62728',
                    bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.7))

    # Update title with label info
    title_suffix = f' (Samples: {len(actuals)})'
    if show_values:
        title_suffix += f' - Values shown every {step} samples'
    
    plt.title(f'Best Model Performance - Epoch {best_epoch}{title_suffix}', 
             fontsize=17, fontweight='bold')
    plt.xlabel('Sample Index', fontsize=13)
    plt.ylabel('FCT (seconds)', fontsize=13)
    plt.xticks(sample_indices[::step], fontsize=11)
    plt.yticks(fontsize=11)
    plt.legend(fontsize=14, loc='upper right', framealpha=0.9, fancybox=True, shadow=True)
    plt.grid(True, alpha=0.3, linestyle=':', linewidth=0.5)
    
    # Adjust layout to accommodate labels
    plt.subplots_adjust(top=0.9, bottom=0.15)
    
    # Ensure the save directory exists
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    plt.savefig(save_path, dpi=300)
    print(f"Best epoch prediction timeseries plot saved to: {save_path}")
    plt.close()

# ==================== 【新增】推理数据可视化函数 ====================
def plot_predictions_vs_actuals_inference(prediction_data: dict, save_path: str):
    """
    Plots a scatter plot of predicted vs. actual values for inference results.
    """
    preds = np.array(prediction_data['predictions'])
    actuals = np.array(prediction_data['actuals'])

    plt.figure(figsize=(10, 10))
    # Plot scatter with enhanced styling
    plt.scatter(actuals, preds, alpha=0.6, s=30, color='#1f77b4', edgecolors='white', 
               linewidths=0.5, label='Predicted vs. Actual (Inference)')
    
    # Plot y=x diagonal line for reference
    lims = [
        np.min([plt.xlim(), plt.ylim()]),  # min of both axes
        np.max([plt.xlim(), plt.ylim()]),  # max of both axes
    ]
    plt.plot(lims, lims, '--', color='#d62728', alpha=0.8, linewidth=2.5, zorder=0, 
            label='Perfect Prediction (y=x)')
    plt.xlim(lims)
    plt.ylim(lims)
    
    # Calculate statistics
    rmse = np.sqrt(np.mean((preds - actuals) ** 2))
    mae = np.mean(np.abs(preds - actuals))
    r2 = np.corrcoef(preds, actuals)[0, 1] ** 2 if len(preds) > 1 else 0
    
    # Add statistics text box with inference metadata
    metadata = prediction_data.get('inference_metadata', {})
    stats_text = f'Inference Results\nSamples: {metadata.get("num_samples", len(preds))}\nRMSE: {rmse:.4f}\nMAE: {mae:.4f}\nR²: {r2:.4f}'
    plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, fontsize=12,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    plt.title('Inference Results: Predicted FCT vs. Actual FCT', fontsize=17, fontweight='bold')
    plt.xlabel('Actual FCT (seconds)', fontsize=13)
    plt.ylabel('Predicted FCT (seconds)', fontsize=13)
    plt.xticks(fontsize=11)
    plt.yticks(fontsize=11)
    plt.legend(fontsize=14, loc='upper left', framealpha=0.9, fancybox=True, shadow=True)
    plt.grid(True, alpha=0.3, linestyle=':', linewidth=0.5)
    plt.gca().set_aspect('equal', adjustable='box')
    
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    plt.savefig(save_path, dpi=300)
    print(f"Inference prediction comparison plot saved to: {save_path}")
    plt.close()

def plot_error_distribution_inference(prediction_data: dict, save_path: str):
    """
    Plots a histogram of the prediction errors for inference results.
    """
    preds = np.array(prediction_data['predictions'])
    actuals = np.array(prediction_data['actuals'])
    errors = preds - actuals

    plt.figure(figsize=(12, 7))
    plt.hist(errors, bins=50, alpha=0.7, color='#2ca02c', edgecolor='white', linewidth=0.5)
    
    mean_error = np.mean(errors)
    std_error = np.std(errors)
    
    plt.axvline(mean_error, color='#d62728', linestyle='--', linewidth=2.5, 
               label=f'Mean Error: {mean_error:.4f}')
    plt.axvline(mean_error + std_error, color='#ff7f0e', linestyle=':', linewidth=2, 
               label=f'+1 Std: {mean_error + std_error:.4f}')
    plt.axvline(mean_error - std_error, color='#ff7f0e', linestyle=':', linewidth=2, 
               label=f'-1 Std: {mean_error - std_error:.4f}')
    
    metadata = prediction_data.get('inference_metadata', {})
    plt.title(f'Inference Error Distribution (Samples: {metadata.get("num_samples", len(preds))}, Mean={mean_error:.4f}, Std={std_error:.4f})', 
             fontsize=17, fontweight='bold')
    plt.xlabel('Prediction Error (seconds)', fontsize=13)
    plt.ylabel('Frequency', fontsize=13)
    plt.xticks(fontsize=11)
    plt.yticks(fontsize=11)
    plt.legend(fontsize=12, framealpha=0.9, fancybox=True, shadow=True)
    plt.grid(True, alpha=0.3, linestyle=':', linewidth=0.5)
    
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    plt.savefig(save_path, dpi=300)
    print(f"Inference error distribution plot saved to: {save_path}")
    plt.close()

def plot_predictions_timeseries_inference(prediction_data: Dict, save_path: str, show_values: bool = True, value_step: Optional[int] = None):
    """
    Plots a bar chart of predicted vs. actual values for inference results.
    """
    preds = np.array(prediction_data['predictions'])
    actuals = np.array(prediction_data['actuals'])
    num_samples = len(actuals)
    sample_indices = np.arange(num_samples)
    bar_width = 0.35

    plt.figure(figsize=(15, 8))
    plt.bar(sample_indices - bar_width/2, actuals, bar_width, label='Actual Values (Ground Truth)',
            color='#1f77b4', alpha=0.8)
    plt.bar(sample_indices + bar_width/2, preds, bar_width, label='Predicted Values (Inference)',
            color='#d62728', alpha=0.8)
    
    # Calculate statistics
    rmse = np.sqrt(np.mean((preds - actuals) ** 2))
    mae = np.mean(np.abs(preds - actuals))
    r2 = np.corrcoef(preds, actuals)[0, 1] ** 2 if len(preds) > 1 else 0
    
    # Determine step for label display first
    if show_values:
        if value_step is None:
            if num_samples <= 20:
                step = 1
            elif num_samples <= 50:
                step = 2
            elif num_samples <= 100:
                step = 5
            else:
                step = 10
        else:
            step = value_step
    else:
        step = 1
    
    # Add statistics text box
    metadata = prediction_data.get('inference_metadata', {})
    stats_text = f'Inference Results\nSamples: {metadata.get("num_samples", len(preds))}\nRMSE: {rmse:.4f}\nMAE: {mae:.4f}\nR²: {r2:.4f}'
    if show_values:
        stats_text += f'\nLabels: Every {step} samples'
    plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, fontsize=12,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    # Set Y-axis range with extra space for labels
    y_min = min(min(actuals), min(preds)) * 0.9
    y_max = max(max(actuals), max(preds)) * 1.15 # Increase top margin for labels
    plt.ylim(y_min, y_max)
    
    # Add value labels on data points
    if show_values:
        y_offset = (y_max - y_min) * 0.01
        
        for i in range(0, num_samples, step):
            actual_val = actuals[i]
            pred_val = preds[i]
            
            if actual_val < 0.001:
                actual_str = f'{actual_val:.1e}'
            else:
                actual_str = f'{actual_val:.2f}'
            
            if pred_val < 0.001:
                pred_str = f'{pred_val:.1e}'
            else:
                pred_str = f'{pred_val:.2f}'
            
            plt.text(i - bar_width/2, actual_val + y_offset, actual_str,
                    fontsize=8, ha='center', va='bottom', color='#1f77b4',
                    bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.7))
            plt.text(i + bar_width/2, pred_val + y_offset, pred_str,
                    fontsize=8, ha='center', va='bottom', color='#d62728',
                    bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.7))

    title_suffix = f' (Samples: {len(actuals)})'
    if show_values:
        title_suffix += f' - Values shown every {step} samples'
        
    plt.title(f'Inference Results: Predicted vs. Actual FCT{title_suffix}', 
             fontsize=17, fontweight='bold')
    plt.xlabel('Sample Index', fontsize=13)
    plt.ylabel('FCT (seconds)', fontsize=13)
    plt.xticks(sample_indices[::step], fontsize=11)
    plt.yticks(fontsize=11)
    plt.legend(fontsize=14, loc='upper right', framealpha=0.9, fancybox=True, shadow=True)
    plt.grid(True, alpha=0.3, linestyle=':', linewidth=0.5)
    
    plt.subplots_adjust(top=0.9, bottom=0.15)
    
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    plt.savefig(save_path, dpi=300)
    print(f"Inference prediction timeseries plot saved to: {save_path}")
    plt.close()

def plot_predictions_four_subplots(prediction_data: Dict, save_path: str, epoch: Optional[int] = None):
    """
    Create a visualization with 4 subplots, each showing a portion of samples with actual values, 
    predicted values (bar chart) and errors (line chart).
    
    Args:
        prediction_data (dict): Dictionary containing 'predictions' and 'actuals' lists
        save_path (str): Path to save the image
        epoch (Optional[int]): Current epoch number for title display
    """
    preds = np.array(prediction_data['predictions'])
    actuals = np.array(prediction_data['actuals'])
    errors = preds - actuals
    num_samples = len(actuals)
    
    # Scientific plotting color scheme
    colors = {
        'actual': '#2E86AB',      # Deep sea blue
        'predicted': '#A23B72',   # Deep rose red
        'error': '#F18F01',       # Golden orange
        'grid': '#E8E8E8'         # Light gray
    }
    
    # Divide samples into 4 equal parts
    samples_per_subplot = num_samples // 4
    remainder = num_samples % 4
    
    # Create 2x2 subplot layout
    fig, axes = plt.subplots(2, 2, figsize=(20, 12))
    fig.suptitle(f'Detailed Analysis of Sample Prediction Results (Total Samples: {num_samples})' + 
                 (f' - Epoch {epoch}' if epoch is not None else ''), 
                 fontsize=20, fontweight='bold', y=0.96)
    
    # Explicitly disable all grids for all subplots and set light gray background
    for ax_row in axes:
        for ax in ax_row:
            ax.grid(False)
            ax.set_facecolor('#f8f8f8')  # Light gray background
    
    # Calculate global statistics
    global_rmse = np.sqrt(np.mean(errors ** 2))
    global_mae = np.mean(np.abs(errors))
    global_r2 = np.corrcoef(preds, actuals)[0, 1] ** 2 if len(preds) > 1 else 0
    
    # Assign samples to each subplot
    start_idx = 0
    for i in range(2):
        for j in range(2):
            subplot_idx = i * 2 + j
            
            # Calculate sample range for current subplot
            if subplot_idx < remainder:
                end_idx = start_idx + samples_per_subplot + 1
            else:
                end_idx = start_idx + samples_per_subplot
            
            # Extract data for current subplot
            current_actuals = actuals[start_idx:end_idx]
            current_preds = preds[start_idx:end_idx]
            current_errors = errors[start_idx:end_idx]
            current_indices = np.arange(len(current_actuals))
            
            # Get current subplot
            ax1 = axes[i, j]
            
            # Draw bar chart (actual and predicted values)
            bar_width = 0.35
            bars1 = ax1.bar(current_indices - bar_width/2, current_actuals, bar_width, 
                           label='Actual Values', color=colors['actual'], alpha=0.8, 
                           edgecolor='white', linewidth=0.8)
            bars2 = ax1.bar(current_indices + bar_width/2, current_preds, bar_width, 
                           label='Predicted Values', color=colors['predicted'], alpha=0.8, 
                           edgecolor='white', linewidth=0.8)
            
            # Set left y-axis (delay values)
            ax1.set_xlabel(f'Sample Index (Range: {start_idx}-{end_idx-1})', fontsize=11, fontweight='bold')
            ax1.set_ylabel('FCT Delay (seconds)', fontsize=11, fontweight='bold', color='black')
            ax1.tick_params(axis='y', labelcolor='black', labelsize=10)
            ax1.tick_params(axis='x', labelsize=10)
            ax1.grid(False)  # Explicitly disable grid
            
            # Create right y-axis (error values)
            ax2 = ax1.twinx()
            line = ax2.plot(current_indices, current_errors, 'o-', color=colors['error'], 
                          linewidth=2.5, markersize=5, markeredgecolor='white', 
                          markeredgewidth=1, label='Prediction Error', alpha=0.9)
            ax2.set_ylabel('Prediction Error (seconds)', fontsize=11, fontweight='bold', color=colors['error'])
            ax2.tick_params(axis='y', labelcolor=colors['error'], labelsize=10)
            ax2.grid(False)  # Explicitly disable grid for right y-axis
            
            # Add value labels (keep 3 decimal places)
            if len(current_actuals) <= 15:  # Only show labels when sample count is small
                y_range_ax1 = ax1.get_ylim()[1] - ax1.get_ylim()[0]
                y_offset_ax1 = y_range_ax1 * 0.03
                
                for idx, (actual, pred) in enumerate(zip(current_actuals, current_preds)):
                    # Add values on bar chart
                    ax1.text(idx - bar_width/2, actual + y_offset_ax1, f'{actual:.3f}',
                            ha='center', va='bottom', fontsize=8, color=colors['actual'],
                            bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8))
                    ax1.text(idx + bar_width/2, pred + y_offset_ax1, f'{pred:.3f}',
                            ha='center', va='bottom', fontsize=8, color=colors['predicted'],
                            bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8))
                
                # Add error values on line chart
                for idx, error in enumerate(current_errors):
                    ax2.text(idx, error, f'{error:.3f}', ha='center', va='bottom', 
                            fontsize=8, color=colors['error'],
                            bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8))
            
            # Calculate statistics for current subplot
            local_rmse = np.sqrt(np.mean(current_errors ** 2))
            local_mae = np.mean(np.abs(current_errors))
            local_r2 = np.corrcoef(current_preds, current_actuals)[0, 1] ** 2 if len(current_preds) > 1 else 0
            
            # Add statistics text box
            stats_text = f'Samples: {len(current_actuals)}\nRMSE: {local_rmse:.3f}\nMAE: {local_mae:.3f}\nR²: {local_r2:.3f}'
            ax1.text(0.02, 0.98, stats_text, transform=ax1.transAxes, fontsize=9,
                    verticalalignment='top', bbox=dict(boxstyle='round,pad=0.4', 
                    facecolor='lightblue', alpha=0.8))
            
            # Set subplot title
            ax1.set_title(f'Subplot {subplot_idx+1}: Samples {start_idx} - {end_idx-1}', 
                         fontsize=13, fontweight='bold', pad=10)
            
            # Merge legends
            lines1, labels1 = ax1.get_legend_handles_labels()
            lines2, labels2 = ax2.get_legend_handles_labels()
            ax1.legend(lines1 + lines2, labels1 + labels2, loc='best', 
                      fontsize=10, framealpha=0.9, fancybox=True, shadow=True)
            ax1.set_ylim(top=0.40)
            start_idx = end_idx
    
    # Add global statistics
    global_stats_text = f'Global Statistics | Total Samples: {num_samples} | RMSE: {global_rmse:.3f} | MAE: {global_mae:.3f} | R²: {global_r2:.3f}'
    fig.text(0.5, 0.02, global_stats_text, ha='center', fontsize=12, fontweight='bold',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='lightyellow', alpha=0.9))
    
    # Adjust layout
    plt.tight_layout(rect=[0, 0.06, 1, 0.94])
    
    # Save image
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Four-subplot prediction visualization saved to: {save_path}")
    plt.close()

def plot_training_metrics_four_subplots(metrics_data: dict, save_path: str):
    """
    Create a visualization with 4 subplots showing training metrics:
    1. Training & Validation Loss
    2. Validation MAE
    3. Validation RMSE
    4. Validation MAPE
    
    Args:
        metrics_data (dict): Dictionary containing training_metrics list
        save_path (str): Path to save the image
    """
    # Extract data from JSON
    training_metrics = metrics_data['training_metrics']
    
    epochs = [metric['epoch'] for metric in training_metrics]
    train_loss = [metric['training_loss'] for metric in training_metrics]
    val_loss = [metric['validation_loss'] for metric in training_metrics]
    val_mae = [metric['validation_mae'] for metric in training_metrics]
    val_rmse = [metric['validation_rmse'] for metric in training_metrics]
    
    # Process MAPE (remove % and convert to float)
    val_mape = [float(metric['validation_mape'].rstrip('%')) for metric in training_metrics]
    
    # Scientific plotting color scheme
    colors = {
        'train': '#2E86AB',       # Deep sea blue
        'val': '#A23B72',         # Deep rose red
        'mae': '#F18F01',         # Golden orange
        'rmse': '#2CA02C',        # Green
        'mape': '#9467BD'         # Purple
    }
    
    # Create 2x2 subplot layout
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Training Metrics Analysis', fontsize=20, fontweight='bold', y=0.96)
    
    # Set light gray background for all subplots
    for ax_row in axes:
        for ax in ax_row:
            ax.grid(False)
            ax.set_facecolor('#f8f8f8')  # Light gray background
    
    # 1. Training & Validation Loss (Top Left)
    ax1 = axes[0, 0]
    ax1.plot(epochs, train_loss, 'o-', color=colors['train'], linewidth=2.5, 
             markersize=5, markeredgecolor='white', markeredgewidth=1, 
             label='Training Loss', alpha=0.9)
    ax1.plot(epochs, val_loss, 's-', color=colors['val'], linewidth=2.5, 
             markersize=5, markeredgecolor='white', markeredgewidth=1, 
             label='Validation Loss', alpha=0.9)
    
    ax1.set_title('Training & Validation Loss', fontsize=14, fontweight='bold', pad=10)
    ax1.set_xlabel('Epoch', fontsize=12, fontweight='bold')
    ax1.set_ylabel('Loss', fontsize=12, fontweight='bold')
    ax1.legend(fontsize=11, framealpha=0.9, fancybox=True, shadow=True)
    ax1.tick_params(labelsize=10)
    
    # Add min values annotation
    min_train_idx = np.argmin(train_loss)
    min_val_idx = np.argmin(val_loss)
    ax1.annotate(f'Min Train: {train_loss[min_train_idx]:.6f}\n(Epoch {epochs[min_train_idx]})',
                xy=(epochs[min_train_idx], train_loss[min_train_idx]), 
                xytext=(0.02, 0.98), textcoords='axes fraction',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='lightblue', alpha=0.8),
                fontsize=9, verticalalignment='top')
    ax1.annotate(f'Min Val: {val_loss[min_val_idx]:.6f}\n(Epoch {epochs[min_val_idx]})',
                xy=(epochs[min_val_idx], val_loss[min_val_idx]), 
                xytext=(0.02, 0.78), textcoords='axes fraction',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='lightcoral', alpha=0.8),
                fontsize=9, verticalalignment='top')
    
    # 2. Validation MAE (Top Right)
    ax2 = axes[0, 1]
    ax2.plot(epochs, val_mae, 'o-', color=colors['mae'], linewidth=2.5, 
             markersize=5, markeredgecolor='white', markeredgewidth=1, alpha=0.9)
    
    ax2.set_title('Validation MAE', fontsize=14, fontweight='bold', pad=10)
    ax2.set_xlabel('Epoch', fontsize=12, fontweight='bold')
    ax2.set_ylabel('MAE', fontsize=12, fontweight='bold')
    ax2.tick_params(labelsize=10)
    
    # Add min/max values
    min_mae_idx = np.argmin(val_mae)
    max_mae_idx = np.argmax(val_mae)
    ax2.annotate(f'Min: {val_mae[min_mae_idx]:.6f}\n(Epoch {epochs[min_mae_idx]})',
                xy=(epochs[min_mae_idx], val_mae[min_mae_idx]), 
                xytext=(0.02, 0.98), textcoords='axes fraction',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgreen', alpha=0.8),
                fontsize=9, verticalalignment='top')
    ax2.annotate(f'Max: {val_mae[max_mae_idx]:.6f}\n(Epoch {epochs[max_mae_idx]})',
                xy=(epochs[max_mae_idx], val_mae[max_mae_idx]), 
                xytext=(0.02, 0.78), textcoords='axes fraction',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='lightyellow', alpha=0.8),
                fontsize=9, verticalalignment='top')
    
    # 3. Validation RMSE (Bottom Left)
    ax3 = axes[1, 0]
    ax3.plot(epochs, val_rmse, 'o-', color=colors['rmse'], linewidth=2.5, 
             markersize=5, markeredgecolor='white', markeredgewidth=1, alpha=0.9)
    
    ax3.set_title('Validation RMSE', fontsize=14, fontweight='bold', pad=10)
    ax3.set_xlabel('Epoch', fontsize=12, fontweight='bold')
    ax3.set_ylabel('RMSE', fontsize=12, fontweight='bold')
    ax3.tick_params(labelsize=10)
    
    # Add min/max values
    min_rmse_idx = np.argmin(val_rmse)
    max_rmse_idx = np.argmax(val_rmse)
    ax3.annotate(f'Min: {val_rmse[min_rmse_idx]:.6f}\n(Epoch {epochs[min_rmse_idx]})',
                xy=(epochs[min_rmse_idx], val_rmse[min_rmse_idx]), 
                xytext=(0.02, 0.98), textcoords='axes fraction',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgreen', alpha=0.8),
                fontsize=9, verticalalignment='top')
    ax3.annotate(f'Max: {val_rmse[max_rmse_idx]:.6f}\n(Epoch {epochs[max_rmse_idx]})',
                xy=(epochs[max_rmse_idx], val_rmse[max_rmse_idx]), 
                xytext=(0.02, 0.78), textcoords='axes fraction',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='lightyellow', alpha=0.8),
                fontsize=9, verticalalignment='top')
    
    # 4. Validation MAPE (Bottom Right)
    ax4 = axes[1, 1]
    ax4.plot(epochs, val_mape, 'o-', color=colors['mape'], linewidth=2.5, 
             markersize=5, markeredgecolor='white', markeredgewidth=1, alpha=0.9)
    
    ax4.set_title('Validation MAPE', fontsize=14, fontweight='bold', pad=10)
    ax4.set_xlabel('Epoch', fontsize=12, fontweight='bold')
    ax4.set_ylabel('MAPE (%)', fontsize=12, fontweight='bold')
    ax4.tick_params(labelsize=10)
    
    # Add min/max values
    min_mape_idx = np.argmin(val_mape)
    max_mape_idx = np.argmax(val_mape)
    ax4.annotate(f'Min: {val_mape[min_mape_idx]:.2f}%\n(Epoch {epochs[min_mape_idx]})',
                xy=(epochs[min_mape_idx], val_mape[min_mape_idx]), 
                xytext=(0.02, 0.98), textcoords='axes fraction',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgreen', alpha=0.8),
                fontsize=9, verticalalignment='top')
    ax4.annotate(f'Max: {val_mape[max_mape_idx]:.2f}%\n(Epoch {epochs[max_mape_idx]})',
                xy=(epochs[max_mape_idx], val_mape[max_mape_idx]), 
                xytext=(0.02, 0.78), textcoords='axes fraction',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='lightyellow', alpha=0.8),
                fontsize=9, verticalalignment='top')
    
    # Add global statistics
    final_train_loss = train_loss[-1]
    final_val_loss = val_loss[-1]
    final_mae = val_mae[-1]
    final_rmse = val_rmse[-1]
    final_mape = val_mape[-1]
    
    global_stats_text = (f'Final Epoch ({epochs[-1]}) | Train Loss: {final_train_loss:.6f} | '
                        f'Val Loss: {final_val_loss:.6f} | MAE: {final_mae:.6f} | '
                        f'RMSE: {final_rmse:.6f} | MAPE: {final_mape:.2f}%')
    fig.text(0.5, 0.02, global_stats_text, ha='center', fontsize=11, fontweight='bold',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='lightyellow', alpha=0.9))
    
    # Adjust layout
    plt.tight_layout(rect=[0, 0.06, 1, 0.94])
    
    # Save image
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Training metrics four-subplot visualization saved to: {save_path}")
    plt.close()

def main():
    """Main function to load data and call plotting functions."""
    parser = argparse.ArgumentParser(description='Script to visualize model training results.')
    parser.add_argument('--history_file', type=str, default='training_history.json', help='Path to the training history JSON file.')
    parser.add_argument('--predictions_file', type=str, default='inference_results_visualization.json', help='Path to the prediction results JSON file.')
    parser.add_argument('--metrics_file', type=str, default='training_metric.json', help='Path to the training metrics JSON file.')
    parser.add_argument('--output_dir', type=str, default='visualizations', help='Directory to save the plots.')
    parser.add_argument('--inference_mode', action='store_true', help='Enable inference mode to visualize inference results.')
    args = parser.parse_args()

    # --- Load Data ---
    try:
        with open(args.history_file, 'r') as f:
            history = json.load(f)
        print(f"Successfully loaded training history: {args.history_file}")
    except FileNotFoundError:
        print(f"Error: Could not find training history file {args.history_file}")
        history = None

    try:
        with open(args.predictions_file, 'r') as f:
            predictions = json.load(f)
        print(f"Successfully loaded prediction results: {args.predictions_file}")
    except FileNotFoundError:
        print(f"Error: Could not find prediction results file {args.predictions_file}")
        predictions = None

    try:
        with open(args.metrics_file, 'r') as f:
            metrics = json.load(f)
        print(f"Successfully loaded training metrics: {args.metrics_file}")
    except FileNotFoundError:
        print(f"Error: Could not find training metrics file {args.metrics_file}")
        metrics = None

    # --- Plotting ---
    if history:
        plot_loss_curve(history, os.path.join(args.output_dir, 'loss_curve.png'))
        # 【修改】只有在非推理模式下才绘制最好epoch的预测值时序图
        if not args.inference_mode:
            plot_best_epoch_predictions_timeseries(history, os.path.join(args.output_dir, 'best_epoch_predictions_timeseries.png'))
    
    if predictions:
        # 【修改】根据模式调整图表标题和文件名
        if args.inference_mode:
            # 推理模式：调整图表以突出这是推理结果
            plot_predictions_vs_actuals_inference(predictions, os.path.join(args.output_dir, 'inference_predictions_vs_actuals.png'))
            plot_error_distribution_inference(predictions, os.path.join(args.output_dir, 'inference_error_distribution.png'))
            plot_predictions_timeseries_inference(predictions, os.path.join(args.output_dir, 'inference_predictions_timeseries.png'))
        else:
            # 训练模式：保持原有行为
            plot_predictions_vs_actuals(predictions, os.path.join(args.output_dir, 'predictions_vs_actuals.png'))
            plot_error_distribution(predictions, os.path.join(args.output_dir, 'error_distribution.png'))
            plot_predictions_timeseries(predictions, os.path.join(args.output_dir, 'predictions_timeseries_final.png'))
        
        # Generate four-subplot visualization
        plot_predictions_four_subplots(predictions, os.path.join(args.output_dir, 'predictions_four_subplots.png'))

    if metrics:
        # Generate training metrics four-subplot visualization
        plot_training_metrics_four_subplots(metrics, os.path.join(args.output_dir, 'training_metrics_four_subplots.png'))

    print("\nVisualization complete!")


if __name__ == '__main__':
    main()
