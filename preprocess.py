# preprocess.py 用来提前预处理训练数据，更快一些 
import torch
import numpy as np
import json
import os
import argparse
from tqdm import tqdm
from collections import defaultdict
import itertools

# 导入您的项目模块
from clos_topo import Clos

# 从您的代码中复制的核心类和函数
class LinkGraphBuilder:
    def __init__(self, clos_topology: Clos):
        self.clos_topology = clos_topology
        self.link_to_idx = {}
        self.idx_to_link = {}
        self.edge_index = None
        self.num_links = 0
        self._build_link_mapping()
        self._build_link_graph()

    def _build_link_mapping(self):
        # 确保边的顺序一致，以便重现性
        sorted_edges = sorted(self.clos_topology.G.edges())
        self.link_to_idx = {(src, dst): i for i, (src, dst) in enumerate(sorted_edges)}
        self.idx_to_link = {i: link for link, i in self.link_to_idx.items()}
        self.num_links = len(self.link_to_idx)

    def _build_link_graph(self):
        edge_list = []
        switch_to_links = defaultdict(list)
        for link_idx, (src, dst) in self.idx_to_link.items():
            if src.startswith(('S', 'P')): switch_to_links[src].append(link_idx)
            if dst.startswith(('S', 'P')): switch_to_links[dst].append(link_idx)
        for _, connected_link_indices in switch_to_links.items():
            if len(connected_link_indices) > 1:
                for u, v in itertools.permutations(connected_link_indices, 2):
                    edge_list.append([u, v])
        if edge_list:
            unique_edges = list(set(map(tuple, edge_list)))
            self.edge_index = torch.tensor(unique_edges, dtype=torch.long).t().contiguous()
        else:
            self.edge_index = torch.empty((2, 0), dtype=torch.long)

    def get_path_link_indices(self, path: list) -> list:
        """将节点路径转换为链路索引列表，如果路径无效返回None"""
        if len(path) < 2: 
            return None
        
        indices = []
        for i in range(len(path) - 1):
            link_idx = self.link_to_idx.get((path[i], path[i+1]))
            if link_idx is None:
                return None  # 如果任何链路无效，返回None
            indices.append(link_idx)
        
        return indices

    def get_total_links(self) -> int:
        return self.num_links

def compute_global_statistics(data_files: list) -> tuple[float, float]:
    """计算全局流大小统计信息用于标准化"""
    all_flow_sizes = []
    print("正在计算全局流大小统计信息...")
    for batch_file in tqdm(data_files, desc="计算统计"):
        try:
            with open(batch_file, 'r') as f:
                data = json.load(f)
            all_flows = data.get('flows', [])
            for flow in all_flows:
                flow_size = flow['inputs']['flow_features'][0]
                all_flow_sizes.append(flow_size)
        except Exception as e:
            print(f"警告：跳过文件 {batch_file}: {e}")
            continue
    

    
    all_flow_sizes_np = np.array(all_flow_sizes)
    mean = float(np.mean(all_flow_sizes_np))
    std = float(np.std(all_flow_sizes_np))
    return mean, std

def count_flows_statistics(root_dir: str) -> dict:
    """统计所有数据集中的流数量
    
    Args:
        root_dir: 数据集根目录
        
    Returns:
        dict: 包含各种流数量统计信息的字典
    """
    print("="*50)
    print("开始统计流数量...")
    
    # 构建拓扑（用于验证路径有效性）
    clos = Clos(4, 4, 4, ps_bandwidth=16000, sh_bandwidth=16000)
    clos.build()
    link_builder = LinkGraphBuilder(clos)
    
    statistics = {
        'total_flows': 0,
        'total_files': 0,
        'datasets': {},
        'valid_flows': 0,
        'invalid_flows': 0,
        'flows_by_start_time': defaultdict(int),
        'flow_size_distribution': {
            'min': float('inf'),
            'max': float('-inf'),
            'mean': 0.0,
            'total_size': 0.0
        }
    }
    
    # 遍历所有可能的数据集分割
    for split in ['train', 'validation', 'test']:
        split_dir = os.path.join(root_dir, split)
        if not os.path.exists(split_dir):
            print(f"警告：找不到目录 {split_dir}，跳过。")
            continue
            
        split_files = [os.path.join(split_dir, f) for f in os.listdir(split_dir) if f.endswith('.json')]
        
        split_stats = {
            'total_flows': 0,
            'total_files': len(split_files),
            'valid_flows': 0,
            'invalid_flows': 0,
            'flows_by_path_length': defaultdict(int),
            'unique_start_times': set(),
            'avg_flows_per_file': 0.0
        }
        
        print(f"\n统计 {split} 数据集，共 {len(split_files)} 个文件...")
        
        for file_path in tqdm(split_files, desc=f"统计 {split}"):
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                
                all_flows = data.get('flows', [])
                file_flow_count = len(all_flows)
                
                split_stats['total_flows'] += file_flow_count
                statistics['total_flows'] += file_flow_count
                
                # 统计每个文件中的流
                for flow in all_flows:
                    # 流大小统计
                    flow_size = flow['inputs']['flow_features'][0]
                    statistics['flow_size_distribution']['total_size'] += flow_size
                    statistics['flow_size_distribution']['min'] = min(statistics['flow_size_distribution']['min'], flow_size)
                    statistics['flow_size_distribution']['max'] = max(statistics['flow_size_distribution']['max'], flow_size)
                    
                    # 开始时间统计
                    start_time = flow['inputs']['start_time']
                    split_stats['unique_start_times'].add(start_time)
                    statistics['flows_by_start_time'][start_time] += 1
                    
                    # 路径有效性验证
                    path = flow['inputs']['path']
                    path_links = link_builder.get_path_link_indices(path)
                    
                    if path_links is None:
                        split_stats['invalid_flows'] += 1
                        statistics['invalid_flows'] += 1
                    else:
                        split_stats['valid_flows'] += 1
                        statistics['valid_flows'] += 1
                        # 统计路径长度
                        path_length = len(path_links)
                        split_stats['flows_by_path_length'][path_length] += 1
                        
            except Exception as e:
                print(f"错误处理文件 {file_path}: {e}")
                continue
        
        # 计算平均值
        if split_stats['total_files'] > 0:
            split_stats['avg_flows_per_file'] = split_stats['total_flows'] / split_stats['total_files']
        
        # 转换set为数量
        split_stats['unique_start_times'] = len(split_stats['unique_start_times'])
        
        statistics['datasets'][split] = split_stats
        statistics['total_files'] += split_stats['total_files']
    
    # 计算全局平均流大小
    if statistics['total_flows'] > 0:
        statistics['flow_size_distribution']['mean'] = statistics['flow_size_distribution']['total_size'] / statistics['total_flows']
    
    # 打印统计结果
    print("\n" + "="*50)
    print("流数量统计结果:")
    print("="*50)
    
    print(f"📊 总体统计:")
    print(f"   • 总流数量: {statistics['total_flows']:,}")
    print(f"   • 总文件数: {statistics['total_files']:,}")
    print(f"   • 有效流数量: {statistics['valid_flows']:,}")
    print(f"   • 无效流数量: {statistics['invalid_flows']:,}")
    print(f"   • 有效流比例: {statistics['valid_flows']/statistics['total_flows']*100:.2f}%")
    
    print(f"\n📈 流大小分布:")
    print(f"   • 最小流大小: {statistics['flow_size_distribution']['min']:,.0f}")
    print(f"   • 最大流大小: {statistics['flow_size_distribution']['max']:,.0f}")
    print(f"   • 平均流大小: {statistics['flow_size_distribution']['mean']:,.2f}")
    print(f"   • 总流量: {statistics['flow_size_distribution']['total_size']:,.0f}")
    
    print(f"\n🔗 数据集分布:")
    for split, split_stats in statistics['datasets'].items():
        print(f"   • {split.upper()}:")
        print(f"     - 流数量: {split_stats['total_flows']:,}")
        print(f"     - 文件数: {split_stats['total_files']:,}")
        print(f"     - 平均每文件流数: {split_stats['avg_flows_per_file']:.1f}")
        print(f"     - 有效流: {split_stats['valid_flows']:,}")
        print(f"     - 无效流: {split_stats['invalid_flows']:,}")
        print(f"     - 唯一开始时间: {split_stats['unique_start_times']:,}")
        
        # 路径长度分布
        if split_stats['flows_by_path_length']:
            print(f"     - 路径长度分布:")
            for length, count in sorted(split_stats['flows_by_path_length'].items()):
                print(f"       * 长度 {length}: {count:,} 流")
    
    print(f"\n⏰ 时间分布:")
    unique_start_times = len(statistics['flows_by_start_time'])
    print(f"   • 唯一开始时间: {unique_start_times:,}")
    print(f"   • 平均每时间点流数: {statistics['total_flows']/unique_start_times:.1f}")
    
    # 显示最繁忙的时间点
    if statistics['flows_by_start_time']:
        most_busy_time = max(statistics['flows_by_start_time'].items(), key=lambda x: x[1])
        print(f"   • 最繁忙时间点: {most_busy_time[0]:.6f}s ({most_busy_time[1]} 流)")
    
    print("="*50)
    
    return statistics

def preprocess_data(root_dir: str, output_dir: str, show_stats: bool = True):
    """主预处理函数，现在包含同步拥塞特征计算"""
    print("="*50)
    print("开始预处理（包含同步拥塞特征计算）...")
    
    # 首先显示数据统计概览
    if show_stats:
        print("\n首先统计数据概览...")
        count_flows_statistics(root_dir)
        print("\n现在开始预处理...")
    
    # 构建拓扑
    clos = Clos(4, 4, 4, ps_bandwidth=16000, sh_bandwidth=16000)
    clos.build()
    link_builder = LinkGraphBuilder(clos)
    num_links = link_builder.get_total_links()

    # 仅从训练集计算标准化统计信息
    train_dir_raw = os.path.join(root_dir, 'train')
    train_files_raw = [os.path.join(train_dir_raw, f) for f in os.listdir(train_dir_raw) if f.endswith('.json')]
    global_mean, global_std = compute_global_statistics(train_files_raw)
    

    print(f"全局统计: mean={global_mean:.4f}, std={global_std:.4f}")

    # 保存元数据
    os.makedirs(output_dir, exist_ok=True)
    metadata = {
        'edge_index': link_builder.edge_index,
        'num_links': num_links,
        'global_mean': global_mean,
        'global_std': global_std
    }
    torch.save(metadata, os.path.join(output_dir, 'metadata.pt'))
    print(f"元数据已保存至 {os.path.join(output_dir, 'metadata.pt')}")

    # 只处理训练集和验证集（测试集用于推理，无需预处理）
    for split in ['train', 'validation']:
        source_dir = os.path.join(root_dir, split)
        target_dir = os.path.join(output_dir, split)
        os.makedirs(target_dir, exist_ok=True)
        
        if not os.path.exists(source_dir):
            print(f"警告：找不到源目录 {source_dir}，跳过。")
            continue

        source_files = [os.path.join(source_dir, f) for f in os.listdir(source_dir) if f.endswith('.json')]
        
        print(f"\n处理 {split} 数据集，共 {len(source_files)} 个文件...")
        # 分批处理文件，每批100个文件 
        for file_path in tqdm(source_files, desc=f"Processing {split}"):
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                
                all_flows_raw = data.get('flows', [])

                # 【新增】步骤1: 按开始时间对所有流进行分组
                flows_by_start_time = defaultdict(list)
                for flow in all_flows_raw:
                    path_links = link_builder.get_path_link_indices(flow['inputs']['path'])
                    # 跳过无效路径或路径长度不正确的流
                    if path_links is None or len(path_links) != 4: 
                        continue
                    
                    start_time = flow['inputs']['start_time']
                    flow_size = flow['inputs']['flow_features'][0]
                    norm_size = (flow_size - global_mean) / global_std
                    
                    flows_by_start_time[start_time].append({
                        'original_flow': flow,
                        'path_links': path_links,
                        'path_links_set': set(path_links),  # 用于快速O(1)查找
                        'norm_size': norm_size
                    })

                processed_flow_info_list = []
                
                # 【新增】步骤2: 遍历每个同步流组，计算拥塞特征
                for start_time, sync_flows_group in flows_by_start_time.items():
                    # 对于组内每个流，计算其与其他流的拥塞情况
                    for i, target_flow in enumerate(sync_flows_group):
                        max_sync_count = 0
                        max_sync_volume = 0.0
                        
                        # 遍历目标流路径上的每条链路，找到瓶颈
                        for link_idx in target_flow['path_links']:
                            current_link_sync_count = 0
                            current_link_sync_volume = 0.0
                            
                            # 检查同一同步组中其他流的拥塞情况
                            for j, other_flow in enumerate(sync_flows_group):
                                if i == j:  # 不与自己比较
                                    continue
                                
                                # 如果其他流也使用了这条链路
                                if link_idx in other_flow['path_links_set']:
                                    current_link_sync_count += 1
                                    current_link_sync_volume += other_flow['norm_size']
                            
                            # 保持最大值以找到瓶颈
                            max_sync_count = max(max_sync_count, current_link_sync_count)
                            max_sync_volume = max(max_sync_volume, current_link_sync_volume)

                        # 【新增】步骤3: 创建包含10个元素的特征向量
                        original_flow = target_flow['original_flow']
                        start_time = original_flow['inputs']['start_time']
                        time_delay = original_flow['time_delay']
                        end_time = start_time + time_delay
                        
                        # 新的结构: [start, end, norm_size, p1, p2, p3, p4, delay, sync_count, sync_volume]
                        info_vector = [
                            start_time,
                            end_time,
                            target_flow['norm_size'],
                            *target_flow['path_links'],  # 展开4个路径链路索引
                            time_delay,
                            float(max_sync_count),      # 同步拥塞流数量
                            float(max_sync_volume)      # 同步拥塞流量
                        ]
                        processed_flow_info_list.append(info_vector)

                if not processed_flow_info_list: 
                    continue
                
                # 按开始时间排序，保持原始顺序
                processed_flow_info_list.sort(key=lambda x: x[0])
                flows_tensor = torch.tensor(processed_flow_info_list, dtype=torch.float32)
                
                save_path = os.path.join(target_dir, os.path.basename(file_path).replace('.json', '.pt'))
                torch.save(flows_tensor, save_path)

            except Exception as e:
                print(f"错误处理文件 {file_path}: {e}")
                continue

    print("\n预处理完成！")
    print("特征向量现在包含10个元素: [start, end, norm_size, p1, p2, p3, p4, delay, sync_count, sync_volume]")
    print("注意：测试集不需要预处理，推理时使用动态特征提取")
    print("="*50)

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='数据预处理脚本（包含同步拥塞特征）')
    parser.add_argument('--datasets_dir', type=str, default='data_gen/datasets', help='原始数据集根目录')
    parser.add_argument('--output_dir', type=str, default='datasets_processed', help='预处理后数据的输出目录')
    parser.add_argument('--count_only', action='store_true', help='仅统计流数量，不执行预处理')
    parser.add_argument('--save_stats', type=str, help='保存统计结果到JSON文件（可选）')
    parser.add_argument('--no_stats', action='store_true', help='预处理时不显示统计信息')
    args = parser.parse_args()
    
    if args.count_only:
        # 只执行流数量统计
        stats = count_flows_statistics(args.datasets_dir)
        
        # 如果指定了保存路径，将统计结果保存为JSON
        if args.save_stats:
            # 转换统计结果为JSON可序列化格式
            json_stats = {}
            for key, value in stats.items():
                if key == 'flows_by_start_time':
                    # 将defaultdict转换为普通dict
                    json_stats[key] = dict(value)
                elif isinstance(value, dict):
                    json_stats[key] = {}
                    for sub_key, sub_value in value.items():
                        if isinstance(sub_value, dict) and 'flows_by_path_length' in sub_value:
                            # 处理嵌套的defaultdict
                            json_stats[key][sub_key] = dict(sub_value)
                            json_stats[key][sub_key]['flows_by_path_length'] = dict(sub_value['flows_by_path_length'])
                        else:
                            json_stats[key][sub_key] = sub_value
                else:
                    json_stats[key] = value
            
            with open(args.save_stats, 'w') as f:
                json.dump(json_stats, f, indent=2, ensure_ascii=False)
            print(f"\n💾 统计结果已保存到: {args.save_stats}")
    else:
        # 执行完整预处理
        preprocess_data(args.datasets_dir, args.output_dir, show_stats=not args.no_stats)
